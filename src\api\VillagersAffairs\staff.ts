/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-10-31 14:28:25
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-11-06 18:21:30
 * @FilePath: \szxcy-gov-management\src\api\VillagersAffairs\Staff.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/villagerAffairs/staff'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取工作人员列表
export const getStaffListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增工作人员前，对手机号和姓名进行是否有用户的校验
export const checkExistUserByPhoneAndNameApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/users/user/checkExistUserByPhoneAndName',
    params
  })
  return res && res.data
}

// 新增工作人员
export const addStaffApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑工作人员
export const editStaffApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 启用/禁用工作人员
export const changeStaffStatusApi = async (id: string, params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/switchOnOff/${id}`),
    params
  })
  return res && res.data
}

// 删除工作人员
export const delStaffApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 获取工作人员详情
export const getStaffDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 工作人员的下拉列表
export const getStaffDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/comboBox`),
    params
  })
  return res && res.data
}

// 判断当前登录人是否为村民办事的工作人员
export const judgeIsStaffOrNot = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/judgeIsStaffOrNot`)
  })
  return res && res.data
}
