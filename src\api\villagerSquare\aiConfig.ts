/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-08-07 09:27:27
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-09-02 11:14:14
 * @FilePath: \szxcy-gov-management\src\api\villagerSquare\aiConfig.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/ruralCircle/config'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取AI配置列表
export const getAIListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增AI配置
export const addAIItemApi = async (data: Partial<Recordable>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑AI配置
export const editAIItemApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// AI配置列表开启。关闭
export const onOffAIApi = async (ids: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${ids}`)
  })
  return res && res.data
}

// AI授权页面
export const getAIAuthorizeApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/list/getAuthPageUrl`),
    params: { configId: id }
  })
  return res && res.data
}

// ----------------------AI能力页面--------------
// 获取AI能力详情
export const getAIDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 获取AI能力:设备列表
export const getAIDeviceListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/list/getDevicesByRegionCon`),
    params
  })
  return res && res.data
}

// 获取AI能力:设备能力列表
export const getAIDeviceAbilityListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/list/getDeviceAiAbilityList`),
    params
  })
  return res && res.data
}

// 获取AI能力:获取垃圾满溢算法设置
export const getDetectionSettingApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/list/overflowAndExposureDetection`),
    params
  })
  return res && res.data
}

// 设备能力列表开启/关闭
export const onOffAICapabilityApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/list/aiconfig/deviceCode`),
    data
  })
  return res && res.data
}

// 垃圾满溢检测：绑定
export const bindOverflowApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/bindoverflow`),
    data
  })
  return res && res.data
}

// 垃圾满溢检测：解绑
export const unbindOverflowApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/unbindoverflow`),
    data
  })
  return res && res.data
}

// 垃圾满溢监测：开启
export const onOverflowApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/turnOn`),
    data
  })
  return res && res.data
}

// 垃圾满溢监测：关闭
export const offOverflowApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/turnOff`),
    data
  })
  return res && res.data
}

// 推送警示录：开/关
export const subscribeOverflowApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/aiconfig/subscribeAiSceneMessage`),
    data
  })
  return res && res.data
}

// 获取AI能力:设备视频流、图片流
// export const getDeviceMediaUrlApi = async (params: Recordable) => {
//   const res = await request.get<IResponse>({
//     url: getUrl(`/list/getDeviceMediaUrlRtsp`),
//     params
//   })
//   return res && res.data
// }

// 获取AI能力:设备视频流、图片流
export const getDeviceMediaUrlApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/list/getDeviceMediaUrlImg`),
    params
  })
  return res && res.data
}

// 垃圾满溢配置
export const settingConfigOverApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/list/aiconfig/configOver`),
    data
  })
  return res && res.data
}
