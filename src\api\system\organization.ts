import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { OrganizationType, DepartmentsType, OrgDeptTreeDataType } from './types'
import { PREFIX_DEPT, PREFIX_ORGS, getOrgAndDeptTreeWithOptionsApi } from './noPermissionData'

const request = useAxios()

// 基础路径
const BASE_URL_ORG = '/system/organizations'
const BASE_URL_DEP = '/system/departments'

// 获取路径
const getOrgUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_ORG, path)
}
const getDepUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_DEP, path)
}

// 获取组织列表
export const getOrganizationListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<OrganizationType>>>({
    url: getOrgUrl(),
    params
  })
  return res && res.data
}

// 获取组织树形列表
export const getOrganizationtreeApi = async () => {
  const res = await request.get<IResponse<OrganizationType[]>>({
    url: getOrgUrl('tree')
  })
  return res && res.data
}

// 添加组织
export const addOrganizationApi = async (data: OrganizationType) => {
  const res = await request.post<IResponse<OrganizationType>>({
    url: getOrgUrl(),
    data
  })
  return res && res.data
}

// 删除组织
export const delOrganizationApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getOrgUrl(id)
  })
  return res && res.data
}

// 更新组织
export const updateOrganizationApi = async (data: OrganizationType) => {
  const res = await request.put<IResponse>({
    url: getOrgUrl(data.id),
    data
  })
  return res && res.data
}

// 根据id查询组织
export const getOrganizationDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getOrgUrl(id)
  })
  return res && res.data
}

// 获取部门列表
export const getDepartmentsListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<DepartmentsType>>>({
    url: getDepUrl(),
    params
  })
  return res && res.data
}

// 获取部门树
export const getDepartmentsTreeApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getDepUrl(`${id}/tree`)
  })

  return res && res.data
}

// 获取部门详情
export const getDepartmentsDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getDepUrl(id)
  })
  return res && res.data
}

// 添加部门
export const addDepartmentsApi = async (data: DepartmentsType) => {
  const res = await request.post<IResponse<DepartmentsType>>({
    url: getDepUrl(),
    data
  })
  return res && res.data
}

// 删除部门
export const delDepartmentsApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getDepUrl(id)
  })
  return res && res.data
}

// 更新部门
export const updateDepartmentsApi = async (data: DepartmentsType) => {
  const res = await request.put<IResponse>({
    url: getDepUrl(data.id),
    data
  })
  return res && res.data
}

// 获取组织/部门树形列表
export const getOrgAndDeptTreeApi = async (
  isIdAddPrefix = true, //是否需要在组织/部门ID前加前缀
  orgIdPrefix = PREFIX_ORGS, // 组织前缀
  deptIdPrefix = PREFIX_DEPT // 部门前缀
): Promise<OrgDeptTreeDataType[]> => {
  return getOrgAndDeptTreeWithOptionsApi({ isIdAddPrefix, orgIdPrefix, deptIdPrefix })
}

// 获取部门树
export const getOrgInfoTreeApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: `/org/info/${id}`
  })

  return res && res.data
}

// 根据租户获取下级组织
export const getOrgInfoTreeByParentApi = async (id: string, tenantId?: string) => {
  const res = await request.get<IResponse>({
    url: `/org/info/byParent?id=${id}&tenantId=${tenantId ? tenantId : ''}`
  })

  return res && res.data
}

// 根据租户获取本级组织
export const getOrgInfoTreeByIdApi = async (id: string, tenantId?: string) => {
  const res = await request.get<IResponse>({
    url: `/org/info/byId?id=${id}&tenantId=${tenantId ? tenantId : ''}`
  })

  return res && res.data
}
