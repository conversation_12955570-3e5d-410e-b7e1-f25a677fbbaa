export interface CategoryType {
  parentId?: string
  parentIds?: string[]
  id: string
  name: string
  code: string
  orderNo: number
  disabled?: boolean
}

export interface FormType {
  id?: string
  categoryId: string
  name: string
  code: string
  createTime: string
  state: number // 1: 已部署，0：未部署
  formConfigJson: string
  permissionId: string
}

export interface FormDataType {
  [proName: string]: string
}

export interface QueryType {
  logicOp: string
  conditions: ConditionType[]
}

export interface ConditionType {
  field: string
  op: string
  value1?: string | string[]
  value12: string | string[]
}

export interface QuerySearchType {
  queries: QueryType
}

export enum ConditionDescEnum {
  like = 'like',
  eq = 'eq',
  neq = 'neq',
  between = 'between',
  gt = 'gt',
  egt = 'egt',
  lt = 'lt',
  elt = 'elt'
}
