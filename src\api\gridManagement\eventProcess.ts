import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { EventProcessType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/gridEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取事件处理列表
export const getEventProcessListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<EventProcessType>>>({
    url: getUrl('/getPageListPC'),
    params
  })

  return res && res.data
}

//获取事件处理详情
export const getEventProcessDetailApi = async (params: Recordable) => {
  const res = await request.get<IResponse<EventProcessType>>({
    url: getUrl('/getDetail'),
    params
  })

  return res && res.data
}

//删除事件处理
export const delEventProcessApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}
