/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-10-31 08:39:59
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-06 15:39:37
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\topicManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { topicDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取话题列表
export const getTopicListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<topicDetailType>>>({
    url: getUrl(`/topic`),
    params
  })
  return res && res.data
}

// 获取话题详情
export const getTopicDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/topic/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 添加话题信息
export const addTopicInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/topic'),
    data
  })
  return res && res.data
}

// 编辑话题信息
export const editTopicInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/topic/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个话题
export const delTopicInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/topic/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 启用/禁用单个话题(批量)
export const changeTopicStatusApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/topic/updateStatus`),
    data
  })
  return res && res.data
}

// 获取热门话题列表（新增新鲜事儿使用）
export const getHotTopicListApi = async () => {
  const res = await request.get<IResponse<PageType<topicDetailType>>>({
    url: getUrl(`/topic/hotTopic`),
    params: { size: 10 }
  })
  return res && res.data
}

// 获取热门话题列表（新增新鲜事儿使用）
export const searchTopicListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/topic/searchSelect`),
    params
  })
  return res && res.data
}
