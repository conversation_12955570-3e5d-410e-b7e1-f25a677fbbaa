import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/task'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 转办任务
export const transferWorkflowTaskApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl('transfer'),
    data
  })
  return res && res.data
}

// 退回任务
export const returnWorkflowTaskApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl('returnTask'),
    data
  })
  return res && res.data
}

// 终止任务
export const terminateWorkflowTaskApi = async (data: {
  taskId: string
  procInsId: string
  comment?: string
}) => {
  const res = await request.post<IResponse>({
    url: getUrl('termination'),
    data
  })
  return res && res.data
}

// 审批任务
export const approveWorkflowTaskApi = async (data: {
  taskId: string
  procInsId: string
  comment?: string
  formDatas?: object
}) => {
  const res = await request.post<IResponse>({
    url: getUrl('approve'),
    data
  })
  return res && res.data
}

// 审批任务
export const getWorkflowTaskReturnListApi = async (taskId: string) => {
  const res = await request.post<IResponse<{ id: string; name: string }[]>>({
    url: getUrl(`returnList/${taskId}`)
  })
  return res && res.data
}
