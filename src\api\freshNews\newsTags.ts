/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-10-15 18:13:13
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-06 09:41:53
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\newsTags.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { newsTagType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取标签列表
export const getTagListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<newsTagType>>>({
    url: getUrl(`/tag`),
    params
  })
  return res && res.data
}

// 获取标签详情
export const getTagDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/tag/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 添加标签信息
export const addTagInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/tag'),
    data
  })
  return res && res.data
}

// 编辑标签信息
export const editTagInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/tag/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个标签
export const delTagInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/tag/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 启用/禁用单个标签(批量)
export const changeTagStatusApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/tag/updateStatus`),
    data
  })
  return res && res.data
}

// 获取标签下拉框列表
export const getTagSelectListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<newsTagType>>>({
    url: getUrl(`/tag/select`),
    params
  })
  return res && res.data
}
