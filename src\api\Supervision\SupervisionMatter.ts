/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-09-05 10:45:43
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/supervise/matter'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取督办类型下拉选择
export const getSupervisionTypeDropdownListApi = async () => {
  const res = await request.get<IResponse>({
    url: `/supervise/type/select`
  })
  return res && res.data
}

// 获取督办事项列表
export const getSupervisionMatterListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 督办事项获取编码
export const getMatterCodeApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/getTypeCode')
  })
  return res && res.data
}

// 新增督办事项
export const addSupervisionMatterItemApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑督办事项
export const editSupervisionMatterItemApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 获取督办事项详情(用于编辑)
export const getSupervisionMatterDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 获取督办事项详情(有审核内容)
export const getDetailWithScheduleApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/schedule/${id}`)
  })
  return res && res.data
}

// 撤回督办事项
export const withdrawSupervisionMatterApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/withdraw/${id}`)
  })
  return res && res.data
}

// 删除督办事项
export const delSupervisionMatterItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 审核督办事项
export const examineSupervisionMatterApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/examine/${id}`),
    data
  })
  return res && res.data
}

// 评价督办事项
export const evaluateSupMatterItemApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/evaluate/${id}`),
    data
  })
  return res && res.data
}

// 获取紧急程度的字典表
export const getUrgencyLevelDictionaryApi = async () => {
  const res = await request.get<IResponse>({
    url: `/system/dictionary-items`,
    params: {
      size: 999,
      page: 1,
      catalogId: '7125b321-6ef8-450f-a89d-e72ad3891c10'
    }
  })
  return res && res.data
}
