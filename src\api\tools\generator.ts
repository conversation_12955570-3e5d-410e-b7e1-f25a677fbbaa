import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import filedownload from '@/utils/filedownload'
import type { GenColumnType, GenTableType, PreviewDataType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/generator'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取数据库表列表
export const getTableListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<GenTableType>>>({
    url: getUrl('listByPage'),
    params
  })

  return res && res.data
}

//获取数据库列表
export const getTableListFromDbApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<GenTableType>>>({
    url: getUrl('listByPageForDb'),
    params
  })

  return res && res.data
}

//删除表信息
export const delTableApi = async (ids: string[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl(ids.join(','))
  })

  return res && res.data
}

//同步数据库
export const syncTableApi = async (tableName: string) => {
  const res = await request.get<IResponse>({
    url: getUrl('syncDb/' + tableName)
  })

  return res && res.data
}

//获取预览信息
export const getPreviewApi = async (tableId: string) => {
  const res = await request.get<IResponse<PreviewDataType[]>>({
    url: getUrl('preview/' + tableId)
  })

  return res && res.data
}

//获取代码生成表详情
export const getTableDetailApi = async (tableId: string) => {
  const res = await request.get<
    IResponse<{ info: GenTableType; tables: GenTableType[]; rows: GenColumnType[] }>
  >({
    url: getUrl(tableId)
  })

  return res && res.data
}

// 生成代码
export const genCodeApi = async (tableName: string) => {
  const res = await request.get<IResponse>({
    url: getUrl('genCode/' + tableName)
  })

  return res && res.data
}

// 批量生成代码
export const batchGenCodeApi = async (tableNames: string[]) => {
  const res = await request.get<Blob>({
    url: getUrl('batchGenCode/' + tableNames.join(',')),
    responseType: 'blob'
  })
  let fileName = 'hectdi-boot.zip'
  if (res.data) {
    try {
      if (res.headers['content-disposition']) {
        const match = res.headers['content-disposition'].match(/filename="(.+)"/)
        if (match && match.length > 1) {
          fileName = match[1]
        }
      }
    } catch (error) {}
    filedownload(res.data, fileName)
  }
}

// 生成代码下载
export const codeDownloadApi = async (tableName: string) => {
  const res = await request.get<Blob>({
    url: getUrl('download/' + tableName),
    responseType: 'blob'
  })
  let fileName = 'hectdi-boot.zip'
  if (res.data) {
    try {
      if (res.headers['content-disposition']) {
        const match = res.headers['content-disposition'].match(/filename="(.+)"/)
        if (match && match.length > 1) {
          fileName = match[1]
        }
      }
    } catch (error) {}
    filedownload(res.data, fileName)
  }
}

// 获取数据表字段列表
export const getColumnListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('columnListByPage'),
    params
  })

  return res && res.data
}

// 获取数据表字段列表
export const importTableApi = async (tableNames: string[]) => {
  const res = await request.post<IResponse>({
    url: getUrl('importTable'),
    data: tableNames
  })

  return res && res.data
}

// 更新表信息
export const updateTableApi = async (id: string, data: Partial<GenTableType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(id),
    data
  })

  return res && res.data
}
