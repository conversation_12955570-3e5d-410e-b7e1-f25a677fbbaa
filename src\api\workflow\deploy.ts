import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { DeployType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/deploy'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取流程实例列表
export const getDeployWorkflowListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<DeployType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 删除流程实例
export const delDeployWorkflowApi = async (ids: string[] | string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(ids.toString())
  })
  return res && res.data
}

// 获取流程实例历史列表
export const getDeployWorkflowHistoryApi = async (processKey: string) => {
  const res = await request.get<IResponse<PageType<DeployType>>>({
    url: getUrl(`deploys/${processKey}`),
    params: { size: -1 }
  })
  return res && res.data
}

// 获取已部署流程的xml
export const getDeployWorkflowBpmnXmlApi = async (definitionId: string) => {
  const res = await request.get<IResponse<string>>({
    url: getUrl(`bpmnXml/${definitionId}`)
  })
  return res && res.data
}

// 挂起/激活流程
export const toggleDeployWorkflowApi = async (
  state: 'active' | 'suspended',
  definitionId: string
) => {
  const res = await request.put<IResponse>({
    url: getUrl(),
    params: {
      state,
      definitionId
    }
  })
  return res && res.data
}
