import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { LoginLogsType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/szxcylog/login-logs'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取操作日志管理列表
export const getLoginLogsListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<LoginLogsType>>>({
    url: getUrl(),
    params
  })
  //   console.log(res.data)
  //   res.data.content.forEach((item) => {
  //     item.loginTimeStr = item.loginTime.replace('T', ' ')
  //   })
  //   console.log(res.data)
  return res && res.data
}
