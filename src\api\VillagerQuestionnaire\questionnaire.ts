/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-12-27 11:04:07
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/questionnaire/info'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 村民问卷分页列表
export const getQuestionnaireListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res.data
}

// 新增村民问卷
export const addQuestionnaireApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑村民问卷
export const editQuestionnaireApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 发布村民问卷
export const publishQuestionnaireApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/publish/${id}`)
  })
  return res && res.data
}

// 村民问卷详情（编辑详情）
export const getQuestionnaireDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 删除村民问卷-进入回收站
export const deletQuestionnairenApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 删除村民问卷-彻底删除
export const completelyDeletQuestApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/delete/${id}`)
  })
  return res && res.data
}

// 恢复回收站中的村民问卷
export const recoverQuestionnaireApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/recoverQuestionnaire/${id}`)
  })
  return res && res.data
}

// 填写村民问卷--未登录
export const submitQuestionnaireWithoutLoginApi = async (id: string, data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/submit/${id}`),
    data
  })
  return res && res.data
}
// 填写村民问卷--已登录
export const submitQuestionnaireApi = async (id: string, data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/submitForAuthorized/${id}`),
    data
  })
  return res && res.data
}

// 查看问卷预览详情
export const getQuestionnairePreviewApi = async (id: string, type: number) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getQuestionnaireAndFormInfo/${id}?type=${type}`)
  })
  return res && res.data
}

// 查看问卷填写结果
export const getQuestionnaireResultApi = async (id: string, params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getQuestionnaireFillInfo/${id}`),
    params
  })
  return res && res.data
}

// 问卷列表页基础数据统计
export const getQuestionnaireStatisticBasicApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireStatisticBasic`)
  })
  return res && res.data
}

// 查看问卷发布趋势统计
export const questionnaireTrendStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireTrendStatistic`),
    params
  })
  return res && res.data
}

// 问卷收集趋势统计
export const questionnaireCollectStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireCollectStatistic`),
    params
  })
  return res && res.data
}

// 问卷统计分析页面——问卷投放区域排行（前20）
export const questionnaireCollectionbStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireOrgStatistic`),
    params
  })
  return res && res.data
}

// 问卷统计分析页面——问卷类型分布
export const questionnaireTypeStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireTypeStatistic`),
    params
  })
  return res && res.data
}

// 问卷统计分析页面——问卷进度分布
export const questionnaireProgressStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/questionnaireProgressStatistic`),
    params
  })
  return res && res.data
}

// 问卷统计分析页面——进行中的问卷
export const getListCollectingByPageApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/listCollectingByPage`),
    params
  })
  return res && res.data
}
