/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-05-23 11:20:42
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-06-11 10:32:54
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { populationTagType } from './types'

const request = useAxios()

// 基础路径
// const BASE_URL = '/villagerDetail'
const BASE_URL = '/populationLabel'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取人口标签信息列表
export const getTagInfoListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<populationTagType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取人口标签信息详情
export const getTagInfoDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 添加人口标签信息
export const addTagInfoItemApi = async (data: Partial<populationTagType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除单个人口标签信息
export const delTagInfoItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 删除多个人口标签信息
export const delTagInfoListApi = async (data: Recordable[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl('/deleteBatch') + `?ids=${data.join(',')}`
    // data: { ids: data.join(',') }
  })
  return res && res.data
}

// 编辑人口标签信息
export const editTagInfoItemApi = async (data: Partial<populationTagType>) => {
  const res = await request.put<IResponse>({
    url: getUrl() + `/${data.id}?type=${data.type}`,
    data
  })
  return res && res.data
}

// 获取村民管理处标签下拉框及设置标签
export const getTagListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<populationTagType>>>({
    url: getUrl('/getTagForVillager'),
    params
  })
  return res && res.data
}
