/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-11-04 08:40:56
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-05 10:31:18
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\channelManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { channelDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取频道列表
export const getChannelListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<channelDetailType>>>({
    url: getUrl(`/channel`),
    params
  })
  return res && res.data
}

// 获取频道详情
export const getChannelDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/channel/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 添加频道信息
export const addChannelInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/channel'),
    data
  })
  return res && res.data
}

// 编辑频道信息
export const editChannelInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/channel/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个频道
export const delChannelInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/channel/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 启用/禁用单个频道
export const changeChannelStatusInfoApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/channel/updateStatus/${data.id}`),
    data
  })
  return res && res.data
}

// 获取频道下拉列表
export const getChannelSelectListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/channel/select`),
    params
  })
  return res && res.data
}
