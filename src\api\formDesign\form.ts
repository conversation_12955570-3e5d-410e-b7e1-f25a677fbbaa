import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { FormType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/form/table'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取表单列表
export const getFormListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<FormType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 添加
export const addFormApi = async (data: Partial<FormType>) => {
  const res = await request.post<IResponse<FormType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除
export const delFormApi = async (ids: string[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl(ids[0])
  })
  return res && res.data
}

// 更新表
export const updateFormApi = async (data: FormType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 根据id查询
export const getFormDetailApi = async (id: string) => {
  const res = await request.get<IResponse<FormType>>({
    url: getUrl(id)
  })
  return res && res.data
}

//停用表单
export const stopFormApi = async (ids: string[]) => {
  const res = await request.post<IResponse<FormType>>({
    url: getUrl('deactivate'),
    data: ids
  })
  return res && res.data
}
//发布表单
export const releaseFormApi = async (ids: string[]) => {
  const res = await request.post<IResponse<FormType>>({
    url: getUrl('release'),
    data: ids
  })
  return res && res.data
}

// 根据formId查询权限
export const getPermissionDetailApi = async (formId: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`permission/${formId}`)
  })
  return res && res.data
}

// 根据formId查询详情
export const getDetailByVersionApi = async (formId: string, version?: string) => {
  const params = {}
  if (version) {
    params['version'] = version
  }
  const res = await request.get<IResponse<FormType>>({
    url: getUrl(`${formId}`),
    params
  })
  return res && res.data
}

//设置挂载菜单
export const setPermissionMount = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`mount?&permissionId=${data.permissionId}&formId=${data.id}`)
  })
  return res && res.data
}

//根据url获取数据
export const getPopupData = async (params) => {
  const url = params.url
  delete params.url
  const res = await request.get<IResponse<any>>({
    url: url,
    params
  })
  return res && res.data
}
