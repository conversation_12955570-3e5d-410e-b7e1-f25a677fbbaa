/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-07-17 17:27:33
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/villagerDiscussion'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 村民议事PC端分页列表
export const getDiscussionListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res.data
}

// web端-新增村民议事--基础信息部分
export const addDiscussionBasicInfoApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// web端-新增村民议事--问卷及选项部分
export const addDiscussionQuestionApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/createDiscussionQuestionAndOption`),
    data
  })
  return res && res.data
}

// web端-编辑村民议事--基础信息部分
export const editDiscussionBasicInfoApi = async (id: string, data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// web端-编辑村民议事--问卷及选项部分
export const editDiscussionQuestionApi = async (id: string, data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateDiscussionQuestionAndOption/${id}`),
    data
  })
  return res && res.data
}

// 批量删除村民议事
export const batchDeletDiscussionApi = async (data: Recordable) => {
  const res = await request.delete<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 单个删除村民议事
export const singleDeletDiscussionApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: `/villagerDiscussionDeleteSingle/${id}`
  })
  return res && res.data
}

// PC端查看村民议事详情——编辑时使用
export const getDiscussionContentApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getVillagerDiscussionContentForPC/${id}`)
  })
  return res && res.data
}

// web端-查看议事详情
export const getDiscussionDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getVillagerDiscussionContent/${id}`)
  })
  return res && res.data
}

// 村民议事上架/下架
export const updateDiscussionStatusApi = async (data: number[], status) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateVillagerDiscussionStatus?status=${status}`),
    data
  })
  return res && res.data
}

// 查看议事的投票比例
export const getDiscussionVoteResultApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getVillagerDiscussionVoteResult/${id}`)
  })
  return res && res.data
}

// 查看村民投票与统计
export const getVoteStatisticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: `/discussScope/getVoteStatistics`,
    params
  })
  return res && res.data
}

// 查看某个村民的投票结果
export const getVillagerDiscussionVoteContentsApi = async (discussId: String, userId: string) => {
  const res = await request.get<IResponse>({
    url: `/villagerDiscussionVoteContents/${discussId}`,
    params: {
      userId: userId
    }
  })
  return res && res.data
}

// 获取议事类型字典表
export const getDiscussTypeDictionaryApi = async () => {
  const res = await request.get<IResponse>({
    url: `/system/dictionary-items`,
    params: {
      size: 999,
      page: 1,
      catalogId: 'f7479b99-71aa-41b0-86fa-99c6ae2e94c2'
    }
  })
  return res && res.data
}

// 发布草稿
export const releaseDiscussionDraftApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateVillagerDiscussionDraftStatus/${id}`)
  })
  return res && res.data
}
