import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { CategoryType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL_ORG = '/form/category'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_ORG, path)
}

// 获取表单分类列表
export const getCategoryListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<CategoryType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取表单分类树形列表
export const getCategorytreeApi = async () => {
  const res = await request.get<IResponse<CategoryType[]>>({
    url: getUrl('tree')
  })
  return res && res.data
}

// 添加表单分类
export const addCategoryApi = async (data: CategoryType) => {
  const res = await request.post<IResponse<CategoryType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除表单分类
export const delCategoryApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新表单分类
export const updateCategoryApi = async (data: CategoryType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 根据id查询分类
export const getCategoryDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}
