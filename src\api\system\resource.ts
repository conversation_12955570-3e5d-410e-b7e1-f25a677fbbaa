import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ResourceType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/resources'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取资源列表
export const getResListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ResourceType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 添加资源
export const addResApi = async (data: ResourceType) => {
  const res = await request.post<IResponse<ResourceType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除资源
export const delResApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新资源
export const updateResApi = async (data: ResourceType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}
// 获取资源详情
export const getResourceDetailApi = async (id: string) => {
  const res = await request.get<IResponse<ResourceType>>({
    url: getUrl(id)
  })

  return res && res.data
}
