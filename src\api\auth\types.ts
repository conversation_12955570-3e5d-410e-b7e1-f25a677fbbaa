export interface TokenType {
  clientRegistration: {
    registrationId: string
  }
  principalName: string
  accessToken: {
    tokenValue: string
    issuedAt: string
    expiresAt: string
    tokenType: string
    scopes: string[]
  }
  refreshToken: {
    tokenValue: string
    issuedAt: string
  }
}

export interface RefreshTokenType {
  access_token: string
  refresh_token: string
  scope: string
  token_type: string
  expires_in: number
}

export interface CacheTokenType {
  accessToken: string
  tokenType: string
  refreshToken: string
  interval: number //有效时长
  localAt: number // 本地时间
}
