export interface CategoryType {
  parentIds: string[]
  parentId: string
  id: string
  name: string
  code: string
  sort: number
  remark: string
  deleted: number
  disabled?: boolean
}

export interface WorkflowType {
  modelId: string
  modelName: string
  modelKey: string
  remark: string
  category: string
  parentIds: string[]
  description: string
  formType: number
  formIds: string
  bpmnXml: string
  newVersion: string
}

export interface DeployType {
  definitionId: string
  processName: string
  processKey: string
  category: string
  version: number
  formId: string
  formName: string
  deploymentId: string
  suspended: boolean
  deploymentTime: string
  children: Array<DeployType>
  hasChildren: boolean
  description: string
}

export interface TaskType {
  taskId: string
  taskName: string
  taskDefKey: string
  assigneeId: number
  deptName: string
  startDeptName: string
  assigneeName: string
  startUserId: string
  startUserName: string
  category: string
  procVars: Recordable
  taskLocalVars: Recordable
  deployId: string
  procDefId: string
  procDefKey: string
  procDefName: string
  procDefVersion: number
  procInsId: string
  hisProcInsId: string
  duration: string
  comment: {
    type: string
    comment: string
  }
  candidate: string
  createTime: string
  finishTime: string
  processStatus: string
  procInsKey: string
  procInsName: string
}

export interface WorkflowConfig {
  modelId: string
  formId: string
  icon: string
  formIdsAndVersion: string //表单及版本
  formType: number
  functionScopes: string
  scopeType: number
  scopeUsers: string
  scopeOrgs: string
  scopeDepts: string
  scopeRoles: string
  scopePositions: string
  procName: string
  procKey: string
  procKeyLength: number
}

interface HistoryNodeType {
  procDefId: string
  activityId: string
  activityName: string
  activityType: string
  duration: string
  assigneeId: string
  assigneeName: string
  candidate: string
  commentList: Recordable[]
  createTime: string
  endTime: string
  nodeStatus: string
}

interface FormDataType {
  formId: string
  datas: Recordable
}

export interface TaskDetailType {
  confVO: {
    fieldsPermission: string
    formConfVO: WorkflowConfig
    nextAssigner: boolean
    users: { id: string; name: string }[]
  }
  bpmnXml: string
  historyNodeList: HistoryNodeType[]
  viewerVO: {
    finishedTaskSet: string[]
    finishedSequenceFlowSet: string[]
    unfinishedTaskSet: string[]
    rejectedTaskSet: string[]
  }
  formDatas: FormDataType[]
  procKey: string
  procName: string
}

export interface ProcessFormViewerType {
  name: string
  formId: string
  formJson: object
  datas?: object
}

export interface ProcessCopyType {
  copyId: string
  title: string
  processId: string
  processName: string
  categoryId: string
  deploymentId: string
  instanceId: string
  taskId: string
  userId: string
  originatorId: string
  originatorName: string
  createTime: string
  readFlag: number
}

export interface DraftType {
  draftId: string
  definitionId: string
  objectName: string
  procName: string
  formDataVOS: {
    formId: string
    datas: Recordable
  }[]
  procDesc: string //处理意见
  typeFlag: number //类型 默认0 草稿  1 流程
  copyUserIds: string //抄送用户信息
  nextUserIds: string //下个节点审批人ID
}

export interface DraftListItemType extends DraftType {
  userId: string
  delFlag: number
  createTime: string
  updator: string
  updateTime: string
}
