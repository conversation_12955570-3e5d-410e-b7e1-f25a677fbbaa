import { useAxios } from '@/hooks/web/useAxios'
import type { LabelManageClassType, LabelManageType } from './types'

const request = useAxios()

// 标签类型-列表
export const getTagTypeListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<LabelManageClassType>>>({
    url: '/tagType/listByPage',
    params
  })
  return res && res.data
}
// 标签类型-新增
export const addTagTypeApi = async (params: Recordable) => {
  const res = await request.post<IResponse>({
    url: '/tagType/add',
    params
  })
  return res && res.data
}
// 标签类型-编辑
export const editTagTypeApi = async (data: Partial<LabelManageClassType>) => {
  const res = await request.post<IResponse>({
    url: '/tagType/edit',
    data
  })
  return res && res.data
}
// 标签类型-删除
export const delTagTypeApi = async (params: Recordable) => {
  const res = await request.post<IResponse>({
    url: `/tagType/remove`,
    params
  })
  return res && res.data
}

// 标签类型-上移下移
export const tagTypeMoveApi = async (params?: Recordable) => {
  const res = await request.post<IResponse>({
    url: '/tagType/moveUpAndDown',
    params
  })
  return res && res.data
}

// 标签-列表
export const getTagListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<LabelManageType>>>({
    url: '/tag/listByPage',
    params
  })
  return res && res.data
}
// 批量删除、删除
export const batchDelApi = async (data) => {
  const res = await request.post<IResponse<PageType<LabelManageType>>>({
    url: '/tag/removeBatch',
    data
  })
  return res && res.data
}
// 标签-新增
export const addTagApi = async (data: Partial<LabelManageType>) => {
  const res = await request.post<IResponse>({
    url: '/tag/add',
    data
  })
  return res && res.data
}
// 标签-编辑
export const editTagApi = async (data: Partial<LabelManageType>) => {
  const res = await request.post<IResponse>({
    url: '/tag/edit',
    data
  })
  return res && res.data
}

// 标签-上移下移
export const tagMoveApi = async (params?: Recordable) => {
  const res = await request.post<IResponse>({
    url: '/tag/moveUpAndDown',
    params
  })
  return res && res.data
}
