import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { OragnizationType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/partyOrganizations'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取党组织列表
export const getOragnizationListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<OragnizationType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新建党组织
export const addOragnizationtApi = async (data: Partial<OragnizationType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })

  return res && res.data
}

// 编辑党组织
export const updateOragnizationtApi = async (data: Partial<OragnizationType>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })

  return res && res.data
}

// 获取党组织详情
export const getOragnizationtDetailApi = async (id: string) => {
  const res = await request.get<IResponse<OragnizationType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 党组织删除
export const delOragnizationtApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}

// 党组织删除
export const batchDelOragnizationtApi = async (ids: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(),
    params: { ids }
  })

  return res && res.data
}

// 导入党组织
export const exportOragnizationtsApi = async (data: Recordable) => {
  const res = await request.post<any>({
    responseType: 'blob',
    url: getUrl('/importExcel'),
    data
  })

  return res
}

// 获取当前用户党组织
export const getUserOrgApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/getuserPartyOrganization')
  })
  return res && res.data
}
// 获取党组织联系人
export const getOrgUserApi = async (params: { orgCode: String; tenantId: string }) => {
  console.log(params, '-----')
  const res = await request.get<IResponse>({
    url: getUrl('/getOrganizationUser'),
    params
  })
  return res && res.data
}

// 获取党组织下拉列表
export const getOragnizationOptionsApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<OragnizationType>>>({
    url: getUrl('/dropDownPage'),
    params
  })
  return res && res.data
}
