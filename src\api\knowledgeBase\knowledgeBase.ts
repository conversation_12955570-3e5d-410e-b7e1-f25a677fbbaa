import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { KnowledgeBaseType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/baKnowledgeBase'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 删除知识库
export const delKnowledgeBaseApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 根据id查询
export const getListByIdApi = async (id) => {
  const res = await request.get<IResponse<PageType<KnowledgeBaseType>>>({
    url: getUrl(`${id}`)
  })
  return res && res.data
}

// 查询知识库
export const getTenantListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<KnowledgeBaseType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 查询列表-全部
export const getAllListApi = async (params?: Recordable) => {
  // <IResponse<PageType<KnowledgeBaseType>>>
  const res = await request.get<IResponse>({
    url: getUrl('listAllByPage'),
    params
  })
  return res && res.data
}

// 创建知识库
export const addKnowledgeBaseApi = async (data) => {
  const res = await request.post<IResponse<PageType<KnowledgeBaseType>>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 更新知识库
export const editKnowledgeBaseApi = async (id, data) => {
  const res = await request.put<IResponse<PageType<KnowledgeBaseType>>>({
    url: getUrl(`${id}`),
    data
  })
  return res && res.data
}
