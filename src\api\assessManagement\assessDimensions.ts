/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-07-25 10:02:24
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-07-26 10:53:49
 * @FilePath: \szxcy-gov-management\src\api\assessManagement\assessDimensions.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { assessDimensionType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/assess'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取维度列表
export const getDimensionListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessDimensionType>>>({
    url: getUrl(`/dimension`),
    params
  })
  return res && res.data
}

// 获取维度详情
export const getDimensionDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/dimension/${id}`),
    params: { id }
  })
  return res && res.data
}

// 添加维度信息
export const addDimensionInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/dimension'),
    data
  })
  return res && res.data
}

// 编辑维度信息
export const editDimensionInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/dimension/${data.id}`),
    data
  })
  return res && res.data
}

// 考核维度开启关闭
export const changeDimensionStatusApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/dimension/updateStatus/${data.id}`),
    data: { status: data.status }
  })
  return res && res.data
}

// 删除单个维度
export const delDimensionInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/dimension/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取维度下拉框列表
export const getDimensionDropDownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessDimensionType>>>({
    url: getUrl('/dimension/dropDownList'),
    params
  })
  return res && res.data
}
