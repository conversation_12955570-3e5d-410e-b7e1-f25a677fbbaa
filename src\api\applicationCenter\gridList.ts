import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { gridListType } from '@/api/applicationCenter/types'

const request = useAxios()

// 基础路径
const BASE_URL = '/gridInfo'
const GRID_MEMBER_BASE_URL = '/GridMemberInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

const getGridMemberUrl = (path?: string): string => {
  return resolveUrl(GRID_MEMBER_BASE_URL, path)
}

// 获取网格员列表
export const getGridListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridInfoListByOrgId'),
    params
  })
  return res && res.data
}

export const deleteGridListApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids.join(',')
  }
  const res = await request.delete<IResponse>({
    url: getUrl(`/deleteById/${id}`)
  })
  return res && res.data
}

export const getGridListOnlyOrgTreeApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/getOnlyOrgTree')
  })
  return res && res.data
}

export const getGridMemberApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getGridMemberUrl('/getAllGridMemberManager'),
    params
  })
  return res && res.data
}

export const saveGridApi = async (data: Partial<gridListType>) => {
  const res = await request.post<IResponse>({
    url: getUrl('/saveGrid'),
    data
  })
  return res && res.data
}

export const updateGridApi = async (data: Partial<gridListType>) => {
  const res = await request.put<IResponse>({
    url: getUrl('/updateGrid'),
    data
  })
  return res && res.data
}

export const getGridVillagerPageListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridVillagerPageList'),
    params
  })
  return res && res.data
}

export const getCanSetGridVillagerPageListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getCanSetGridVillagerPageList'),
    params
  })
  return res && res.data
}

export const getGridInfoComboBoxApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridInfoComboBox'),
    params
  })
  return res && res.data
}

export const deleteGridVillagerByIdApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids.join(',')
  }
  const res = await request.delete<IResponse>({
    url: getUrl(`/deleteGridVillagerById/${id}`)
  })
  return res && res.data
}

export const setGridMemberForVillager = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/setGridMemberForVillager`),
    data
  })
  return res && res.data
}

export const setGridVillagerApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/setGridVillager`),
    data
  })
  return res && res.data
}

export const getAllGridMemberManagermApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getGridMemberUrl(`/getAllGridMemberManagerm`),
    params
  })
  return res && res.data
}

// @ts-ignore
export const exportGridListApi = async (data: Recordable) => {
  const res = await request.post<any>({
    responseType: 'blob',
    url: getUrl('/importExcel'),
    data
  })

  return res
}

// @ts-ignore
export const getGridMapApi = async (params: Recordable) => {
  const res = await request.get<any>({
    url: getUrl('/getGridMap'),
    params
  })
  return res.data
}

// 根据Id查询网格详情
export const getGridDetailByIdApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getById/${id}`)
  })
  return res && res.data
}

// 获取区域的rootPath
export const getOrgRootPathApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: 'org/info/getOrgCodeListBySonCode',
    params
  })
  return res && res.data
}

export const taskDataByGrid = async (gridId) => {
  const res = await request.get<any>({
    url: `/processWork/statistic/grid/${gridId}`
  })
  return res.data
}
