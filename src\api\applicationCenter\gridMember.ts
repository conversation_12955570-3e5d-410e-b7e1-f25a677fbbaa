/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-03-06 10:26:43
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/GridMemberInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取网格员列表
export const getGridMemberListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getPageList'),
    params
  })
  return res && res.data
}

//添加网格员
export const addGridMemberItemApi = async (data: Partial<Recordable>) => {
  const res = await request.post<IResponse>({
    url: getUrl('/saveGrid'),
    data
  })
  return res && res.data
}

// 删除选中网格员
export const delGridMemberListApi = async (ids: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/deleteByIds/${ids}`)
  })
  return res && res.data
}

// 编辑网格员
export const editGridMemberItemApi = async (data: Partial<Recordable>) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateGrid`),
    data
  })
  return res && res.data
}

// 查询网格员详情/关联用户列表
export const getGridMemberUserListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<Recordable>>({
    url: getUrl('/gridMemberUserList'),
    params
  })
  return res && res.data
}

// 根据网格员Id获取所辖村民
export const selectVillagerInfoByGridMemberId = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/selectVillagerInfoByGridMemberId'),
    params
  })
  return res && res.data
}

// 网格员批量上传
export const importGridMemberInfoHbx = async (data: Recordable) => {
  const res = await request.post<any>({
    url: getUrl('/importExcel'),
    responseType: 'blob',
    data
  })
  return res
}
