import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { ShareKnowledgeBaseType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/baKnowledgeBase/empower'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 删除
export const delShareApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 根据id查询
// export const getShareListByIdApi = async (id) => {
//   const res = await request.get<IResponse<PageType<ShareKnowledgeBaseType>>>({
//     url: getUrl(`${id}`)
//   })
//   return res && res.data
// }

// 查询列表
export const getShareListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// // 创建
// export const addShareApi = async (data) => {
//   const res = await request.post<IResponse<PageType<ShareKnowledgeBaseType>>>({
//     url: getUrl(),
//     data
//   })
//   return res && res.data
// }

// 更新
export const editShareApi = async (id, data) => {
  const res = await request.put<IResponse<PageType<ShareKnowledgeBaseType>>>({
    url: getUrl(`${id}`),
    data
  })
  return res && res.data
}

// 分享知识库--新增
export const shareApi = async (sourceBaseId, data) => {
  const res = await request.put<IResponse<PageType<ShareKnowledgeBaseType>>>({
    url: getUrl(`shareKnowledge/${sourceBaseId}`),
    data
  })
  return res && res.data
}
