/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-11-19 10:20:48
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-11-20 19:18:37
 * @FilePath: \szxcy-gov-management\src\api\VillagersAffairs\afairTasking.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL1 = '/villagerAffairsTask/unfinishedTasking'
const BASE_URL2 = '/villagerAffairsTask/finishedTasking'

// 获取路径
const getUnfinishedUrl = (path?: string): string => {
  return resolveUrl(BASE_URL1, path)
}
// 获取路径
const getFinishedUrl = (path?: string): string => {
  return resolveUrl(BASE_URL2, path)
}

// 获取村民办事 -事项审批列表
export const getUnfinishedTaskingListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUnfinishedUrl(),
    params
  })
  return res && res.data
}

// 获取村民办事 -事项审批详情
export const getUnfinishedTaskingDetailApi = async (id: string, params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUnfinishedUrl(`/${id}`),
    params
  })
  return res && res.data
}

// 审批村民办事事项-同意/驳回
export const agreeOrAntiApi = async (params: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: `/villagerAffairsTask/agreeOrAnti/${id}`,
    params
  })
  return res && res.data
}

// 获取村民办事 -已办列表
export const getFinishedTaskingListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getFinishedUrl(),
    params
  })
  return res && res.data
}
