import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { newEventType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取新鲜事儿统计数据
export const getNewEventStatisticApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/statisticsByNewAndView`)
  })
  return res && res.data
}

// 获取饼图数据
export const getPieStatisticDataApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/statisticsByChannel`)
  })
  return res && res.data
}

// 状态统计数据
export const getStatusStatisticApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/statisticsByStatus`),
    params
  })
  return res && res.data
}

// 获取文章列表
export const getNewsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<newEventType>>>({
    url: getUrl(`/news`),
    params
  })
  return res && res.data
}

// 获取文章详情
export const getNewsDetailApi = async (data: any) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/${data.id}`),
    params: { type: 1 }
  })
  return res && res.data
}

// 添加文章信息
export const addNewsInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/news'),
    data
  })
  return res && res.data
}

// 编辑文章信息
export const editNewsInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/news/${data.id}`),
    data
  })
  return res && res.data
}

// 置顶文章列表数据
export const onTopNewsListApi = async (params: any) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/topList`),
    params
  })
  return res && res.data
}

// 置顶文章信息
export const onTopNewsApi = async (params: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/news/updateTop/${params.id}`),
    params
  })
  return res && res.data
}

// 删除单个文章
export const delNewsInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/news/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 发布单个文章:审批
export const getNewsApprovalApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/news/examineForApproval`),
    data
  })
  return res && res.data
}

// 发布单个文章
export const publishNewsInfoApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/news/publishNewEvent/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 上架/下架单个文章(批量)
export const changeNewsStatusApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/news/updateShelves`),
    params: data
  })
  return res && res.data
}

// 批量变更频道文章
export const batchChangeNewsApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/news/updateChannel`),
    params: data
  })
  return res && res.data
}

// 获取热门文章列表
export const getTopArticalListApi = async (params: any) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/topList`),
    params
  })
  return res && res.data
}
