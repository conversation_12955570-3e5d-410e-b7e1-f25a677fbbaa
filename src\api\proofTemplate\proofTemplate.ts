import { useAxios } from '@/hooks/web/useAxios'

const request = useAxios()
// 获取证明模版列表
export const getProofTemplateList = async (params) => {
  const res = await request.get<IResponse>({
    url: '/villagerAffairs/proofTemplate',
    params
  })

  return res && res.data
}

// 刪除证明模版
export const delProofTemplate = async (id) => {
  const res = await request.delete<IResponse>({
    url: `/villagerAffairs/proofTemplate/${id}`
  })

  return res && res.data
}

// 获取部门列表
export const getDepartmentManagementCombox = async (params) => {
  const res = await request.get<IResponse>({
    url: '/villagerAffairs/departmentManagement/combox',
    params
  })

  return res && res.data
}
// 新增证明模版
export const addProofTemplateList = async (data) => {
  const res = await request.post<IResponse>({
    url: '/villagerAffairs/proofTemplate',
    data
  })

  return res && res.data
}
// 修改证明模版状态
export const changeProofTemplateStatus = async (data) => {
  const res = await request.put<IResponse>({
    url: `/villagerAffairs/proofTemplate/switchOnOff/${data.id}`,
    params: { templateStatus: data.templateStatus }
  })

  return res && res.data
}
// 获取证明模版详情
export const getProofTemplateDetail = async (id) => {
  const res = await request.get<IResponse>({
    url: `/villagerAffairs/proofTemplate/${id}`
  })

  return res && res.data
}
// 编辑证明模版
export const changeProofTemplate = async (data) => {
  const res = await request.put<IResponse>({
    url: `/villagerAffairs/proofTemplate/${data.id}`,
    data
  })

  return res && res.data
}
