/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-02-05 14:40:33
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-08-29 09:36:15
 * @FilePath: \szxcy-gov-management\src\api\applicationCenter\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 政策信息
export interface policyInfoDetailType {
  id?: string
  title: string
  content: string
  isTop: number
  columnId: string
  status: number
  attachment: string
  orgCode: string
  orgName: string
  columnTopicId: string
  columnName: string
  tenantId: string
  // createdUserName: any
}

// 栏目信息
export interface columnInfoDetailType {
  id?: string
  sort: number | undefined
  columnTopicId: string
  columnName: string
  description: string
  status: any
  lastModifiedTime?: string
}

// 村民信息
export interface villagerInfoDetailType {
  id?: number
  orgCode: string
  gridName: string
  name: string
  mobile: string
  idCardNo: string
  sex: string
  political: string
  relationType: string
  isHouseholdMember: string
  address: string
  marital: string
  education: string
  occupation: string
  work: string
  duties: string
  medical: string
  social: string
  socialNumber: string
  leftoverChildren: string
  lonely: string
  poorHouseholds: string
  lowIncomeHouseholds: string
  preferential: string
  college: string
  disabilities: string
  disabilityLevel: string
  psychopaths: string
  foodSubsidies: string
  drug: string
  letters: string
  flow: string
  enjoyOtherPolicies: string
  otherPolicies: string
  time: string
  dataId: number
  servicemen: string
  householdNum: string
  householdType: string
  householdRelation: string
  score: number
  notes: string
  createdTime: string
  createdId: string
  createdName: string
  gridMemberId: number
  fiveGuarantees: string
  threeNon: string
  povertyAlleviation: string
  gridId: string
  userId: string
  tenantId: string
  povertyAlleviationCheck: string
  orgName: string
  gridMemberName: string
  tags?: any[]
}

// 网格员
export interface gridMemberType {
  id?: number
  type: string
  orgCode: number
  createTime: string
  createUserId: string
  createUserName: string
  updateUserId: string
  updateUserName: string
  tenantId: string
  userId: string
  phone?: number
  houseHouldNum: number
  orgName: string
}

// 网格总览
export interface gridOverviewList {
  VillagerList: {
    orgName: string
    orgId: number
    userName: string
    managerNum: number
    memberNum: number
    gridList: {
      gridId: string
      mapBoundary: null
      gridName: string
      gridCode: string
      orgId: string
      orgName: string
      firstLevelGridManagerName: string
      secondaryGridManagerName: string
      grassrootsGridManager: string
      member: string
      householdNum: number
      eventHandleNum: null
      workDistributionNum: null
      publicVisitNum: null
    }[]
  }[]
  townLeaderName: Function
}
export interface villagerListType {
  orgName: string
  orgId: number
  orgCode: string
  userName: string
  managerNum: number
  memberNum: number
  gridList: {
    gridId: string
    mapBoundary: null
    gridName: string
    gridCode: string
    orgId: string
    orgName: string
    firstLevelGridManagerName: string
    secondaryGridManagerName: string
    grassrootsGridManager: string
    member: string
    householdNum: number
    eventHandleNum: null
    workDistributionNum: null
    publicVisitNum: null
  }[]
}
// 批量新增
export interface batchAddParams {
  label: string
  fakeTableList: {
    prop?: string
    label?: string
  }[]
  fakeTableData: {
    name?: string
    phone?: string
    type?: string
    orgName?: string
  }[]
  // downloadTemplateLink: string
  uploadInterface: Function
}
// 关注村子
export interface followVillagerListType {
  orgCode: string
  userId: string
  createTime: string
  id: string
  orgName: string
  userName: string
}

export interface gridList {
  label: string
  fakeTableList: {
    prop?: string
    label?: string
  }[]
  fakeTableData: {
    name?: string
    phone?: string
    type?: string
    orgName?: string
  }[]
  downloadTemplateLink: string
  uploadInterface: Function
}

export interface gridOrgList {
  id: string
  name: string
  code: string
  parentId: string
  ifVillage: boolean
  ifGrid: boolean
  ifGridMember: boolean
  attendanceId: string
  children?: gridOrgList[]
}

export interface gridListType {
  gridName: string
  gridCode: string
  createTime?: string
  createUserId?: string
  createUserName?: string
  updateTime?: string
  updateUserId?: string
  updateUserName?: string
  orgCode: string
  orgName: string
  longitute?: string
  latitude?: string
  dataSource?: string
  migrationId?: string
  id?: string
  managerId: string
  managerName?: string
  memberIds: string
  tenantId?: string
  villagerCount: string
  phone: string
  mapBoundary?: string
}

export interface populationTagType {
  id?: string
  tagName: string
  tagStatus: number
  tagColor: string
  createTime?: string
  createUserId?: string
  createUserName?: string
  type?: number
}
