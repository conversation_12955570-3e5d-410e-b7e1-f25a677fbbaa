import { useCache } from '@/hooks/web/useCache'
import { ApplicationType, UserType } from '../system/types'
import { CacheTokenType } from './types'

const { wsCache } = useCache()
// 缓存key
const TOKEN = 'TOKEN'
const TOKEN_OBJ = 'TOKEN_OBJ'
const USER_INFO = 'USER_INFO'
const USER_MENUS = 'USER_MENUS'
const USER_PERMISSIONS = 'USER_PERMISSIONS'
const CUR_APPLICATION = 'CUR_APPLICATION' //当前登录的应用
const PASS_UPDATE_UNI_KEY = 'PASSWORD_UPDATE_UNI_SEQ'
const AUTH_SERVICE_HOST_KEY = 'AUTH_SERVICE_HOST_KEY' //授权服务host

const setToken = (token: string) => {
  wsCache.set(TOKEN, token)
}

const getToken = (): string => {
  return wsCache.get(TOKEN) as string
}

const setTokenObj = (tokenObj: CacheTokenType) => {
  wsCache.set(TOKEN_OBJ, tokenObj)
}

const getTokenObj = (): CacheTokenType => {
  return wsCache.get(TOKEN_OBJ) as CacheTokenType
}

const setUserInfo = (user: UserType) => {
  wsCache.set(USER_INFO, user)
}

const getUserInfo = (): UserType => {
  return wsCache.get(USER_INFO) as UserType
}

const setMenus = (menus: any[]) => {
  wsCache.set(USER_MENUS, menus)
}

const getMenus = (): any[] => {
  return wsCache.get(USER_MENUS) as any[]
}

const setPermissions = (permissions: string[]) => {
  wsCache.set(USER_PERMISSIONS, permissions)
}

const getPermissions = (): string[] => {
  return wsCache.get(USER_PERMISSIONS) as string[]
}

const setPasswordUpdateUniKey = (uni: string) => {
  wsCache.set(PASS_UPDATE_UNI_KEY, uni)
}

const getPasswordUpdateUniKey = (): string => {
  return wsCache.get(PASS_UPDATE_UNI_KEY) as string
}

const setCurApplication = (app: Partial<ApplicationType>) => {
  wsCache.set(CUR_APPLICATION, app)
}

const getCurApplication = (): Partial<ApplicationType> => {
  return wsCache.get(CUR_APPLICATION) as Partial<ApplicationType>
}

const setAuthHost = (host: string) => {
  wsCache.set(AUTH_SERVICE_HOST_KEY, host)
}

const getAuthHost = (): string => {
  return wsCache.get(AUTH_SERVICE_HOST_KEY) as string
}

const setItem = (key: string, value: any) => {
  if (key) {
    wsCache.set(key, value)
  }
}

const getItem = (key: string): any => {
  if (key) {
    return wsCache.get(key)
  }
  return ''
}

const removeItem = (key: string) => {
  if (key) {
    wsCache.delete(key)
  }
}

export default {
  setToken,
  getToken,
  setTokenObj,
  getTokenObj,
  setUserInfo,
  getUserInfo,
  setMenus,
  getMenus,
  setPermissions,
  getPermissions,
  setPasswordUpdateUniKey,
  getPasswordUpdateUniKey,
  setCurApplication,
  getCurApplication,
  setAuthHost,
  getAuthHost,
  setItem,
  getItem,
  removeItem
}
