import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ReportType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/reports'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取报表列表
export const getReportListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ReportType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 添加报表
export const addReportApi = async (data: Partial<ReportType>) => {
  const res = await request.post<IResponse<ReportType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除报表
export const delReportApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新报表
// export const updateReportApi = async (data: ReportType) => {
//   const res = await request.put<IResponse>({
//     url: getUrl(data.id),
//     data
//   })
//   return res && res.data
// }
// 获取报表详情
export const getReportDetailApi = async (id: string) => {
  const res = await request.get<IResponse<ReportType>>({
    url: getUrl(id)
  })

  return res && res.data
}

//设置挂载菜单
export const setPermissionMount = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`mount?&permissionId=${data.permissionId}&reportId=${data.id}`)
  })
  return res && res.data
}
