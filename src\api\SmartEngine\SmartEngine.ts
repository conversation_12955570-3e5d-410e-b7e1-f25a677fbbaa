import { useAxios } from '@/hooks/web/useAxios'
const request = useAxios()

// 获取搜索模版列表
export const getSmartEngineTemplateList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/template',
    params
  })

  return res && res.data
}
// 更改模版状态
export const changePlanTemplateStatus = async (taskId: any) => {
  const res = await request.put<IResponse>({
    url: `/plan/template/${taskId}`
  })
  return res && res.data
}
// 获取模版详情
export const getPlanTemplateDetail = async (taskId: any) => {
  const res = await request.get<IResponse>({
    url: `/plan/template/${taskId}`
  })
  return res && res.data
}
// 获取任务中心列表
export const getSmartEnginePlanList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/list',
    params
  })

  return res && res.data
}
// 删除任务中心列表
export const delSmartEnginePlanList = async (id: any) => {
  const res = await request.delete<IResponse>({
    url: '/plan/delete/' + id
  })

  return res && res.data
}
// 启动暂停任务中心列表
export const updateStateSmartEnginePlanList = async (id: any) => {
  const res = await request.put<IResponse>({
    url: '/plan/updateState/' + id
  })

  return res && res.data
}
// 获取搜索模版列表-下拉框
export const getSmartEngineTemplateSelect = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/template/select',
    params
  })

  return res && res.data
}
// 获取频道列表-下拉框
export const getNewEventChannel = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/newEvent/channel',
    params
  })

  return res && res.data
}
// 创建任务中心列表
export const createSmartEnginePlanList = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/plan/create',
    data
  })

  return res && res.data
}
// 编辑任务中心列表
export const updateSmartEnginePlanList = async (data: any, id: any) => {
  const res = await request.put<IResponse>({
    url: '/plan/update/' + id,
    data
  })

  return res && res.data
}
// 獲取任务中心详情
export const getSmartEnginePlanDetail = async (id: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/info/' + id
  })

  return res && res.data
}
// 计划详情-任务列表
export const getPlanLogList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/log/list',
    params
  })

  return res && res.data
}
// 计划详情-任务数据列表
export const getPlanDataList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/data',
    params
  })

  return res && res.data
}
// 任务全部列表
export const getAllPlanList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/log',
    params
  })

  return res && res.data
}
// 獲取任务计划详情
export const getPlanTaskDetail = async (id: any) => {
  const res = await request.get<IResponse>({
    url: '/plan/log/info/' + id
  })

  return res && res.data
}
// 删除任务数据
export const delPlanData = async (data) => {
  const res = await request.delete<IResponse>({
    url: '/plan/data',
    data
  })

  return res && res.data
}
// 发布任务数据
export const pubPlanData = async (data) => {
  const res = await request.post<IResponse>({
    url: '/plan/data/push',
    data
  })

  return res && res.data
}
// 创建自定义任务
export const importPlanData = async (data) => {
  const res = await request.post<IResponse>({
    url: '/plan/data/import',
    data
  })

  return res && res.data
}
