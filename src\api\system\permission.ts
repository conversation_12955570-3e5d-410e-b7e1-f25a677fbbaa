import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { PermissionType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/permissions'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取权限列表
export const getPermissionListApi = async (params?: {}) => {
  const res = await request.get<IResponse<PageType<PermissionType>>>({
    url: getUrl(),
    params
  })

  return res && res.data
}

// 获取权限树
export const getPermissionTreeApi = async (applicationId?: string) => {
  const params: Recordable = {}
  if (applicationId) {
    params.applicationId = applicationId
  }
  const res = await request.get<IResponse<PermissionType[]>>({
    url: getUrl('tree'),
    params
  })

  return res && res.data
}

// 获取权限详情
export const getPermissionDetailApi = async (id: string) => {
  const res = await request.get<IResponse<PermissionType>>({
    url: getUrl(id)
  })

  return res && res.data
}

//新增权限
export const addPermissionApi = async (data: Partial<PermissionType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data: data
  })

  return res && res.data
}

//更新权限
export const updatePermissionApi = async (id: string, data: Partial<PermissionType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(id),
    data: data
  })

  return res && res.data
}

//删除权限
export const delPermissionApi = async (ids: string[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl(ids[0])
  })

  return res && res.data
}

//给权限设置API
export const setPermissionApisApi = async (permissionId: string, apiIds: { id: string }[]) => {
  const res = await request.put<IResponse>({
    url: getUrl(`${permissionId}/apis`),
    data: apiIds
  })

  return res && res.data
}

//根据租户id查询权限列表
export const getPermissionListByTenantApi = async (applicationId?: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`tenant/${applicationId}/list`)
  })

  return res && res.data
}
