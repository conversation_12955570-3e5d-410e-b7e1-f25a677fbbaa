import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ruralCircleListType } from '@/api/villagerSquare/types'

const request = useAxios()

// 基础路径
const BASE_URL = '/ruralCircleColumnContent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取乡情圈列表
export const getRuralCircleListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<ruralCircleListType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// PC端，新增乡情圈内容前，对内容的审核
export const auditRuralCircleItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/examineForApproval'),
    data
  })
  return res && res.data
}

// 新增乡情圈信息
export const addRuralCircleItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(''),
    data
  })
  return res && res.data
}

// 编辑乡情圈信息
export const editRuralCircleItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${data.id}`),
    data
  })
  return res && res.data
}

// 查看乡情圈信息详情
export const getRuralCircleDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}?type=1`)
    // params: { id }
  })
  return res && res.data
}

// 删除单条乡情圈信息
export const delRuralCircleApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`),
    params: { id }
  })
  return res && res.data
}

// 上架/下架乡情圈信息 contentStatus: 2-下架 4-上架
export const onOffRuralCircleItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/onOffShelf/${data.id}?contentStatus=${data.contentStatus}`)
  })
  return res && res.data
}

// 发起乡情圈
export const startRuralCircleItemApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/submitForApproval/${id}`),
    data: { id }
  })
  return res && res.data
}

// 乡情圈:置顶/取消置顶内容
export const topRuralCircleItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/topOrCancelTop/${data.id}?contentTopFlag=${data.contentTopFlag}`)
  })
  return res && res.data
}

// 获取评论分页列表
export const getRuralCircleCommontApi = async (data: any) => {
  const res = await request.get<IResponse>({
    url: `/ruralCircleContentComment`,
    params: data
  })
  return res && res.data
}

// PC端，屏蔽/展示、置顶/取消置顶评论
export const editCommontStatusApi = async (id: string, data: any) => {
  const res = await request.put<IResponse>({
    url: `/ruralCircleContentComment/${id}`,
    data
  })
  return res && res.data
}

// 查看驳回原因
export const getRuralCircleRejectApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/reasonForDisagree/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取所属栏目板块”下拉框列表
export const getColumnPlateOptionApi = async () => {
  const res = await request.get<IResponse>({
    url: '/columnPlateConfig/comboBoxForPc?type=1'
  })
  return res && res.data
}

// 获取栏目的下拉框
export const getColumnOptionApi = async (columnPlateId: string) => {
  const res = await request.get<IResponse>({
    url: `/ruralCircleColumnConfig/comboBoxForPc/${columnPlateId}`,
    params: { columnPlateId }
  })
  return res && res.data
}
