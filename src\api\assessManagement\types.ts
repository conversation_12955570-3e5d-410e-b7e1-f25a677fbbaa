// 绩效等级
export interface performanceRateType {
  id?: number
  orgCode: string
  orgName: string
  name: string
  levelCode?: string
  description?: string
  status?: number
  createdTime: string
  createdUser: string
  tenantId: string
}

// 考核指标
export interface assessIndicatorType {
  id?: number
  orgCode: string
  orgName: string
  name: string
  eventType: number
  score: number
  description?: string
  typeStatus?: number
  createdTime: string
  usedStatus: number
}

// 考核维度
export interface assessDimensionType {
  id?: number
  orgCode: string
  orgName: string
  name: string
  score: number
  description?: string
  status?: number
  createdTime: string
}

// 考核模版
export interface assessTemplateType {
  id?: number
  orgCode: string
  orgName: string
  name: string
  templateCode?: string
  createdUser?: string
  status?: number
  score: number
  createdTime: string
}

// 考核管理
export interface assessManageType {
  id?: number
  orgCode: string
  orgName: string
  name: string
  templateName: string
  templateId: string
  peopleCount: number
  assessStatus: number
  status?: number

  expirationDate?: string
  beginTime: string
  endTime: string
  createdUser: string
  createdTime: string
  description?: string
  tenantId: string

  assessScore: number
  assessProcess: string
  assessResult: number
}
