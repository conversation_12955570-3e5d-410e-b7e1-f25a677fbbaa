import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { taskType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 发起流程
export const addArticleApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl('/startProcess'),
    data
  })

  return res && res.data
}

// 获取待办列表
export const getTaskingListApi = async (params: Recordable) => {
  params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<taskType>>>({
    url: getUrl('/myTasking'),
    params
  })
  return res && res.data
}

// 获取已办列表
export const getTaskedListApi = async (params: Recordable) => {
  params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<taskType>>>({
    url: getUrl('/myTasked'),
    params
  })
  return res && res.data
}

// 审批任务
export const approveTaskApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl('/approveTask'),
    data
  })

  return res && res.data
}

// 待办流程详情
export const taskingDetailApi = async (taskId: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/taskingDetail/${taskId}`)
  })

  return res && res.data
}

// 已办流程详情
export const taskedDetailApi = async (taskId: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/taskedDetail/${taskId}`)
  })

  return res && res.data
}
