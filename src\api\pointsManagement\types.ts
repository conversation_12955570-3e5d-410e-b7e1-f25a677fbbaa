// 积分管理
export interface pointType {
  id: string
  orgName: string
  scoreName: string
  rule: string
  relationType: string
  status: number
  score: number
  orgCode: string
  scoreType: number
}
// 村民积分管理
export interface villagerPointType {
  id: string
  name: string
  sex: string
  orgCode: string
  orgName: string
  political: string
  gridName: string
  scoreTotal: string
  scoreQuarter: string
  score: string
  scoreType: number
  userId: string
}

// // 积分记录
// export interface villagerRecordType {
//   id: string
//   name: string
//   sex: string
//   orgCode: string
//   orgName: string
//   political: string
//   gridName: string
//   scoreTotal: string
//   scoreQuarter: string
//   score: string
//   scoreType: number
// }
