import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { DataMigrationType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/migrationInfo/info'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取列表
export const getMigrationListApi = async (params: Recordable) => {
  if (params.dateRange) {
    params.realStartTime = params.dateRange[0]
    params.realEndTime = params.dateRange[1]
  }
  params.dateRange = undefined
  const res = await request.get<IResponse<PageType<DataMigrationType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取详情
export const getMigrationDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 提交信息
export const addMigrationApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 更新信息
export const updateMigrationApi = async (id: string, data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}
