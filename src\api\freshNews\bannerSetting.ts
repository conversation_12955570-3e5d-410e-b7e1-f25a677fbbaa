/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-11-08 08:40:44
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-11 08:50:16
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\bannerSetting.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { bannerDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取banner列表
export const getBannerSettingListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<bannerDetailType>>>({
    url: getUrl(`/banner`),
    params
  })
  return res && res.data
}

// 获取banner详情
export const getBannerSettingDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/banner/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 添加banner信息
export const addBannerSettingInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/banner'),
    data
  })
  return res && res.data
}

// 编辑banner信息
export const editBannerSettingInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/banner/${data.id}`),
    data
  })
  return res && res.data
}

// 编辑banner：顺序信息
export const editBannerSortApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/banner/updateOrder`),
    data
  })
  return res && res.data
}

// 删除单个banner
export const delBannerSettingInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/banner/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 获取新鲜事儿列表
export const getBannerNewsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/news/search`),
    params
  })
  return res && res.data
}
