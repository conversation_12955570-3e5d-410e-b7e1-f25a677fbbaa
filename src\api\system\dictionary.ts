import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { DictionaryCatalogType, DictionaryItemType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL_CATALOG = '/system/dictionary-catalogs'
const BASE_URL_ITEM = '/system/dictionary-items'

// 获取路径
const getCatalogUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_CATALOG, path)
}
const getItemUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_ITEM, path)
}

// 获取字典列表
export const getDirctionaryCatalogListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<DictionaryCatalogType>>>({
    url: getCatalogUrl(),
    params
  })
  return res && res.data
}

// 获取字典详情
export const getDirctionaryCatalogDetailApi = async (id: string) => {
  const res = await request.get<IResponse<DictionaryCatalogType>>({
    url: getCatalogUrl(id)
  })

  return res && res.data
}

// 获取字典分类树结构
export const getDirctionaryCatalogTreeApi = async () => {
  const res = await request.get<IResponse<DictionaryCatalogType[]>>({
    url: getCatalogUrl('tree')
  })
  return res && res.data
}

// 添加字典
export const addDirctionaryCatalogApi = async (data: DictionaryCatalogType) => {
  const res = await request.post<IResponse<DictionaryCatalogType>>({
    url: getCatalogUrl(),
    data
  })
  return res && res.data
}

// 删除字典
export const delDirctionaryCatalogApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getCatalogUrl(id)
  })
  return res && res.data
}

// 更新字典
export const updateDirctionaryCatalogApi = async (data: DictionaryCatalogType) => {
  const res = await request.put<IResponse>({
    url: getCatalogUrl(data.id),
    data
  })
  return res && res.data
}

// 获取字典项列表
export const getDirctionaryItemListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<DictionaryItemType>>>({
    url: getItemUrl(),
    params
  })
  return res && res.data
}

// 获取字典项详情
export const getDirctionaryItemDetailApi = async (id: string) => {
  const res = await request.get<IResponse<DictionaryItemType>>({
    url: getItemUrl(id)
  })

  return res && res.data
}

// 添加字典项
export const addDirctionaryItemApi = async (data: DictionaryItemType) => {
  const res = await request.post<IResponse<DictionaryItemType>>({
    url: getItemUrl(),
    data
  })
  return res && res.data
}

// 删除字典项
export const delDirctionaryItemApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getItemUrl(id)
  })
  return res && res.data
}

// 更新字典项
export const updateDirctionaryItemApi = async (data: DictionaryItemType) => {
  const res = await request.put<IResponse>({
    url: getItemUrl(data.id),
    data
  })
  return res && res.data
}

// 根据字典类别code获取字典项
export const getDirctionaryItemsByCategoryCodeApi = async (
  code: string
): Promise<DictionaryItemType[]> => {
  const res = await getDirctionaryDetailByCategoryCodeApi(code)
  if (res && res.data && res.data.dictionaryItems) {
    return res.data.dictionaryItems
  }
  return [] as DictionaryItemType[]
}

// 根据字典类别code获取字典类别详情
export const getDirctionaryDetailByCategoryCodeApi = async (code: string) => {
  const res = await request.get<IResponse<DictionaryCatalogType>>({
    url: getCatalogUrl(`code/${code}`)
  })
  return res && res.data
}
