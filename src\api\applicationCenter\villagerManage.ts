/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-06-13 16:32:37
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { villagerInfoDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/villagerDetail'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取村民信息列表
export const getVillagerInfoListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<villagerInfoDetailType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取村民信息详情
export const getVillagerInfoDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 添加村民信息
export const addVillagerInfoItemApi = async (data: Partial<villagerInfoDetailType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除单个村民信息
export const delVillagerInfoItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 删除多个村民信息
export const delVillagerInfoListApi = async (data: Recordable[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl('/deleteByIdList'),
    data
  })
  return res && res.data
}

// 编辑村民信息
export const editVillagerInfoItemApi = async (
  id: string,
  data: Partial<villagerInfoDetailType>
) => {
  const res = await request.put<IResponse>({
    url: getUrl(id),
    data
  })
  return res && res.data
}

// 村民信息批量上传
export const importVillagerInfoHbx = async (data: Recordable) => {
  const res = await request.post<any>({
    url: getUrl('/importExcel'),
    responseType: 'blob',
    data
  })
  return res
}

// 获取网格名称下拉框列表
export const getGridInfoComboBoxApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/gridInfo/getGridInfoComboBox',
    params
  })
  return res && res.data
}

// 村民信息-设置标签
export const setVillagerTagApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: `/villagerTags/${data.villagerId}`,
    data
  })
  return res && res.data
}
