import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { WorkTaskType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/processWork'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取工作任务列表
export const getWorkTaskListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<WorkTaskType>>>({
    url: getUrl(),
    params
  })

  return res && res.data
}
// 获取工作任务数量
export const findPageCount = async (params) => {
  const res = await request.get<IResponse>({
    url: '/processWork/findPageCount',
    params
  })

  return res && res.data
}

//获取工作任务详情
export const getWorkTaskDetailApi = async (id: string) => {
  const res = await request.get<IResponse<WorkTaskType>>({
    url: getUrl(id)
  })

  return res && res.data
}

//新增工作任务
export const addWorkTasktApi = async (data: Partial<WorkTaskType>) => {
  const res = await request.post<IResponse<WorkTaskType>>({
    url: getUrl(),
    data
  })

  return res && res.data
}

//更新工作任务
export const updateWorkTaskApi = async (data: Partial<WorkTaskType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })

  return res && res.data
}

//删除工作任务
export const delWorkTaskApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}

// 获取网格成员列表(指派对象 按个人)
export const getGridMemberListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<Recordable>>>({
    url: getUrl('/getGridMemberList'),
    params
  })

  return res && res.data
}

// 获取工作详情列表
export const getWorkTaskDetailListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<any>>>({
    url: '/workDetail',
    params
  })

  return res && res.data
}

// 获取工作详情列表
export const updateWorkTaskDetailApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: `/workDetail/${data.id}`,
    data
  })

  return res && res.data
}
