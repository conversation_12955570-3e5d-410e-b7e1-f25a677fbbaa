import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ConfigType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/global-configurations'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取全局配置详用户列表
export const getConfigListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ConfigType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 刷新全局配置
export const refreshCacheConfigApi = async () => {
  const res = await request.post<IResponse<PageType<ConfigType>>>({
    url: getUrl('refreshCache')
  })
  return res && res.data
}

// 添加全局配置详
export const addConfigApi = async (data: Partial<ConfigType>) => {
  const res = await request.post<IResponse<ConfigType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除全局配置详
export const delConfigApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新全局配置详
export const updateConfigApi = async (data: Partial<ConfigType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 获取全局配置详情
export const getConfigDetailApi = async (id: string) => {
  const res = await request.get<IResponse<ConfigType>>({
    url: getUrl(id)
  })

  return res && res.data
}
