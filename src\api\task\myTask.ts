import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { myTaskType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/villageCircleTask'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取我的待办列表
export const getMyTaskingListApi = async (params: Recordable) => {
  // params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<myTaskType>>>({
    url: getUrl('/myVillageCircleUnfinishedTasking'),
    params
  })
  return res && res.data
}

// 我的待办审核详情及流程明细
export const myTaskingAudioDetailApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/myVillageCircleUnfinishedTaskingDetail/${params.taskId}`),
    params
  })

  return res && res.data
}

//  我的待办审核
export const audioTaskApi = async (params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/agreeOrAntiVillageCircleContent/${params.id}`),
    params
  })

  return res && res.data
}

// 获取我的已办列表
export const getMyTaskedListApi = async (params: Recordable) => {
  // params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<myTaskType>>>({
    url: getUrl('/myVillageCircleFinishedTasking'),
    params
  })
  return res && res.data
}

// 获取我的待办/已办：统计数据  taskType   0-待办 1-已办
export const getMyTaskStatisticListApi = async (taskType: number) => {
  // params.category = 'article_approval'
  const res = await request.get<IResponse>({
    url: getUrl(`/myVillageCircleTaskStatistic?taskType=${taskType}`)
  })
  return res && res.data
}
