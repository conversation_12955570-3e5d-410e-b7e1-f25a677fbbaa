/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-10-16 16:22:45
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-08 15:16:33
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\growthPointsSetting.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { growthPointsSettingDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取积分设置列表
export const getGrowthPointsSettingListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<growthPointsSettingDetailType>>>({
    url: getUrl(`/growthPoints/rule`),
    params
  })
  return res && res.data
}

// 获取标签详情
export const getGrowthPointsSettingDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/growthPoints/rule/${id}`)
    // params: { id }
  })
  return res && res.data
}

// 编辑积分设置信息
export const editGrowthPointsSettingInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/growthPoints/rule/${data.id}`),
    data
  })
  return res && res.data
}

// 启用、禁用单个积分设置
export const changeStatusGrowthPointsSettingInfoApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/growthPoints/rule/updateStatus/${data.id}`),
    params: data
  })
  return res && res.data
}
