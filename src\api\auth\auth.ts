import axios, { AxiosRequestConfig } from 'axios'
import Encrypt from '@/utils/encrypt'
import type { TokenType, RefreshTokenType, CacheTokenType } from './types'
import type { ApplicationType, PermissionType, UserType } from '../system/types'
import authCache from '@/api/auth/cache'
import { forEach } from '@/utils/tree'
import { isUrl } from '@/utils/is'
import { useCache } from '@/hooks/web/useCache'
import { IS_DEV } from '@/config/app'

const { wsCache } = useCache()

// auth2认证URL相关
const CLIENT_ID = import.meta.env.VITE_CLIENT_ID // 客户端ID
const AUTH2_LOGIN_URL = `/oauth2/authorization/${CLIENT_ID}` // 登录
const AUTH2_LOGIN_URL_OA = '/cas/authorization/office' // 来源系统是OA系统的登录地址

const AUTH2_LOGOUT_URL = '/oauth2/logout' // 登出
const AUTH2_CIPHER_UPDATE = '/passwordUpdatePage' // 修改密码
const AUTH2_TOKEN_URL = '/oauth2/oauth2Token' //获取token
const AUTH2_USER_INFO_URL = '/oauth2/currentUserInfo' //获取用户信息
const AUTH2_REFRESH_TOKEN_URL = '/oauth2/refreshToken' // 刷新token
const AUTH2_GET_HOST_URL = '/oauth2/authorizationUrl' // 获取授权服务域名
// const AUTH2_USER_INFO_URL = '/mock/user/oauth2/currentUserInfo' //mock模拟接口

// 应用来源相关
const FROM_APP_KEY = 'from' // 来源应用，用于从那里来的应用，当前可以从OA系统进入系统
export const FROM_REDIRECT_URL_KEY = '_from_redirect_url_' // redirect url
export const FROM_APP_CACHE_KEY = '_from_app_key_' // 缓存key
const APP_OA_VALUE = 'oa' //  来源oa系统
const FROM_VALID_VALUES = [APP_OA_VALUE] //  来源应用有效值

axios.defaults.withCredentials = true
axios.defaults.headers.common['Accept'] = 'application/json'

export const isLogin = (): boolean => {
  if (authCache.getToken()) return true
  return false
}

export const getToken = (): string | undefined => {
  return authCache.getToken()
}

export const toLogin = () => {
  const queryString = location.search
  let params: URLSearchParams | undefined = undefined
  let redirect = true
  if (queryString) {
    params = new URLSearchParams(queryString)
    const redirectFlag = params.get('redirect')
    redirect = !(redirectFlag && redirectFlag === 'false')
  }
  // 记录当前地址
  if (redirect) {
    authCache.setItem(FROM_REDIRECT_URL_KEY, location.href)
  }
  if (params) {
    const fromValue = params.get(FROM_APP_KEY)
    if (fromValue && FROM_VALID_VALUES.includes(fromValue.toLocaleLowerCase())) {
      authCache.setItem(FROM_APP_CACHE_KEY, APP_OA_VALUE)
      location.href = AUTH2_LOGIN_URL_OA
      return
    }
  }
  authCache.removeItem(FROM_APP_CACHE_KEY)
  location.href = AUTH2_LOGIN_URL
}

// 获取授权服务器地址
export const getAuthorizationUrl = () => {
  if (!authCache.getAuthHost()) {
    const config = {
      headers: {
        Authorization: getToken()
      }
    } as AxiosRequestConfig
    axios.get<string>(AUTH2_GET_HOST_URL, config).then((res) => {
      if (res && res.data) {
        authCache.setAuthHost(res.data)
      }
    })
  }
}

// token是否有效
export const isAuthTokenValid = async (): Promise<boolean> => {
  const config = {
    headers: {
      Authorization: getToken()
    }
  } as AxiosRequestConfig
  return axios
    .get<string>(AUTH2_GET_HOST_URL, config)
    .then(() => {
      return true
    })
    .catch((err) => {
      if (err.response && err.response.status === 401) {
        return false
      }
      return true
    })
}

// 获取登出地址
export const getLogoutUrl = (path = AUTH2_LOGOUT_URL) => {
  let host = authCache.getAuthHost()
  if (host) {
    if (host.endsWith('/')) {
      host = host.substring(0, host.length - 1)
    }
    return host + path
  }
  return ''
}

// ajax登出
export const logoutApi = () => {
  const config = {
    headers: {
      Authorization: getToken()
    }
  } as AxiosRequestConfig
  return axios.get<any>(getLogoutUrl(), config)
}

// 获取修改密码地址
export const getPasswordUpdateUrl = () => {
  let host = authCache.getAuthHost()
  if (host) {
    if (host.endsWith('/')) {
      host = host.substring(0, host.length - 1)
    }
    return host + AUTH2_CIPHER_UPDATE
  }
  return ''
}
// 获取修改密码成功后重定向地址
export const getPasswordUpdateRedirectUrl = () => {
  const unique = Encrypt.SHA224('PASSWORD_UPDATE' + Date.now()).toString()
  authCache.setPasswordUpdateUniKey(unique)
  return location.protocol + '//' + location.host + '/?' + unique
}

export const getMenus = (): any[] | undefined => {
  return authCache.getMenus()
}

export const getUserInfo = (): UserType | undefined => {
  return authCache.getUserInfo()
}

export const getUserPermissions = (): string[] | undefined => {
  return authCache.getPermissions()
}

const pareseToken = (token: TokenType): string => {
  let tokenValue = ''
  if (token.accessToken) {
    tokenValue = token.accessToken.tokenType + ' ' + token.accessToken.tokenValue
    authCache.setToken(tokenValue)
  }

  let localAt = Date.now()
  let interval = 0
  if (token.accessToken.expiresAt && token.accessToken.issuedAt) {
    const issuedAt = new Date(token.accessToken.issuedAt).getTime()
    const expireAt = new Date(token.accessToken.expiresAt).getTime()
    interval = expireAt - issuedAt - 20000
    localAt -= localAt - issuedAt
  }
  const tokenObj: CacheTokenType = {
    accessToken: token.accessToken.tokenValue,
    tokenType: token.accessToken.tokenType,
    refreshToken: token.refreshToken.tokenValue,
    interval,
    localAt
  }
  authCache.setTokenObj(tokenObj)
  return tokenValue
}

export const fetchAuthToken = async (): Promise<TokenType | undefined> => {
  const res = await axios.get<TokenType>(AUTH2_TOKEN_URL).catch(() => {
    if (IS_DEV) {
      console.error('请求accessToken失败')
    }
  })
  if (res && res.data) {
    const token = res.data
    pareseToken(token)
    return token
  } else {
    if (IS_DEV) {
      console.error('获取accessToken失败：', res && res.data)
    }
  }
  return undefined
}

let refreshAuthTokening = false
export const refreshAuthToken = async (): Promise<string | undefined> => {
  if (refreshAuthTokening) {
    // 等待其他已经发起的刷新请求
    while (refreshAuthTokening) {
      await new Promise((resolve) => {
        setTimeout(() => {
          resolve('')
        }, 100)
      })
    }
  }
  refreshAuthTokening = true
  let token = authCache.getToken()
  const tokenObj = authCache.getTokenObj()
  if (tokenObj && tokenObj.refreshToken) {
    const currentTime = Date.now()
    if (currentTime - tokenObj.localAt! >= tokenObj.interval!) {
      // token过期了,需要刷新token
      if (IS_DEV) {
        console.log('token过期了,需要刷新')
      }

      // const data = `registrationClientId=${CLIENT_ID}&username=${
      //   getUserInfo()?.username
      // }&refreshToken=${tokenObj.refreshToken}`
      const data = `access_token=${tokenObj.refreshToken}`
      const res = await axios
        .post<RefreshTokenType>(AUTH2_REFRESH_TOKEN_URL, data, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        })
        .catch(() => {
          if (IS_DEV) {
            console.error('请求accessToken失败')
          }
        })
      if (res && res.data) {
        const refreshTokenObj = res.data
        tokenObj.accessToken = refreshTokenObj.access_token
        tokenObj.refreshToken = refreshTokenObj.refresh_token
        tokenObj.tokenType = refreshTokenObj.token_type
        tokenObj.interval = refreshTokenObj.expires_in * 1000 - 20000 //缩短20秒
        tokenObj.localAt = Date.now()
        token = tokenObj.tokenType + ' ' + tokenObj.accessToken
        authCache.setToken(token)
        authCache.setTokenObj(tokenObj)
      } else {
        if (IS_DEV) {
          console.error('刷新accessToken失败：', res && res.data)
        }
      }
    }
  }
  refreshAuthTokening = false
  return token
}

export const fetchUserInfoApi = async (): Promise<UserType | undefined> => {
  const config = {
    headers: {
      Authorization: getToken()
    }
  } as AxiosRequestConfig
  const res = await axios.get<UserType>(AUTH2_USER_INFO_URL, config).catch(() => {
    if (IS_DEV) {
      console.error('请求用户信息失败')
    }
  })
  if (res && res.data && res.data) {
    const user = res.data
    if (user) {
      if (user.permission && user.permission.children) {
        const permissions: string[] = []
        const tempMenus: Partial<PermissionType>[] = []
        let curApp: Partial<ApplicationType> = {}
        user.permission.children.forEach((app) => {
          if (app && app.children) {
            curApp = { ...app }
            delete curApp['children']

            tempMenus.push(...app.children)
            // 遍历权限,获取按钮权限
            forEach(app.children, (per) => {
              if (per.meta) {
                // 如果有meta数据,则转成对象
                if (typeof per.meta === 'string') {
                  try {
                    per.meta = JSON.parse(per.meta)
                  } catch (error) {
                    per.meta = {}
                  }
                }
              } else {
                per.meta = {}
              }
              // 将权限ID放入到meta中
              if (per.meta && !per.meta['__PERMISSION_ID__']) {
                per.meta['__PERMISSION_ID__'] = per.id
              }
              // 抽取权限信息
              if (per.type === 2 && per.code) {
                permissions.push(per.code)
              }
            })
          }
        })
        // 保存当前登录的应用
        authCache.setCurApplication(curApp)

        const menus: Partial<PermissionType>[] = []
        if (tempMenus.length > 0) {
          for (const menu of tempMenus) {
            let meta: Recordable = {}
            try {
              if (menu.meta) {
                if (typeof menu.meta === 'string') {
                  meta = JSON.parse(menu.meta as string)
                } else {
                  meta = menu.meta
                }
              }
            } catch (error) {}
            if (menu.path && isUrl(menu.path)) {
              menus.push(menu)
            } else {
              if (menu.path) {
                // 根路由必须是/开头
                if (!menu.path.startsWith('/')) {
                  menu.path = '/' + menu.path
                }
              }
              if (menu.component != '#' && !meta.disableLayout) {
                const path = menu.path
                menu.path = ''
                const newMenu: Partial<PermissionType> = {
                  icon: menu.icon,
                  title: menu.title,
                  component: '#',
                  path,
                  children: [menu]
                }
                menus.push(newMenu)
              } else {
                menus.push(menu)
              }
            }
          }
        }

        authCache.setMenus(menus)
        authCache.setPermissions(permissions)
      }
      authCache.setUserInfo(user)
      return user
    }
  } else {
    if (IS_DEV) {
      console.error('获取用户信息失败：', res && res.data)
    }
  }
  return undefined
}

// 检测是否是来自修改密码的重定向,如果是则需要将本地token清空
export const checkFromPasswordUpdate = () => {
  const uni = authCache.getPasswordUpdateUniKey()
  if (uni) {
    const url = location.href
    if (url.indexOf('?' + uni) != -1) {
      // 判断如果是重置密码回来,则需要将本地token清除
      wsCache.clear()
      return true
    }
  }
  return false
}

// 获取重定向地址
export const getRedirectUrl = () => {
  const redirectUrl = authCache.getItem(FROM_REDIRECT_URL_KEY)
  if (redirectUrl) {
    authCache.removeItem(FROM_REDIRECT_URL_KEY)
    return redirectUrl
  }
  return ''
}
