/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-10-31 14:28:25
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-11-13 14:30:52
 * @FilePath: \szxcy-gov-management\src\api\VillagersAffairs\Affairs.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
// const BASE_URL = '/villagerAffairs'

// 获取事项类型路径
const getMatterTypeUrl = (path?: string): string => {
  return resolveUrl('/villagerAffairs/matterType', path)
}
// 获取事项路径
const getMatterUrl = (path?: string): string => {
  return resolveUrl('/villagerAffairs/matterInfo', path)
}

// ****************** 事项类型接口 ***********************
// 获取事项类型列表
export const getAffairTypeListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getMatterTypeUrl(),
    params
  })
  return res && res.data
}

// 新增事项类型
export const addAffairTypeApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getMatterTypeUrl(),
    data
  })
  return res && res.data
}

// 删除事项类型
export const delAffairTypeApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getMatterTypeUrl(`/${id}`)
  })
  return res && res.data
}
// 编辑事项类型
export const editAffairTypeApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getMatterTypeUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 获取事项类型详情
export const getAffairTypeDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getMatterTypeUrl(`/${id}`)
  })
  return res && res.data
}

// 获取事项类型下拉列表
export const getAffairTypeDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getMatterTypeUrl('/comboBox'),
    params
  })
  return res && res.data
}
// ****************** 事项管理接口 ***********************
// 获取证明模板下拉列表
export const getProofTemplateDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/villagerAffairs/proofTemplate/comboBox',
    params
  })
  return res && res.data
}
// 获取事项列表
export const getAffairsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getMatterUrl(),
    params
  })
  return res && res.data
}

// 新增事项
export const addAffairsApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getMatterUrl(),
    data
  })
  return res && res.data
}

// 编辑事项
export const editAffairsApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getMatterUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 上架/下架事项
export const changeAffairsStatusApi = async (id: string, params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getMatterUrl(`/onOffShelf/${id}`),
    params
  })
  return res && res.data
}

// 事项发起审批
export const submitForApprovalApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getMatterUrl(`/submitForApproval/${id}`)
  })
  return res && res.data
}

// 删除事项
export const delAffairsApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getMatterUrl(`/${id}`)
  })
  return res && res.data
}

// 获取事项详情
export const getAffairsDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getMatterUrl(`/${id}`)
  })
  return res && res.data
}

// 事项的下拉列表
export const getAffairsDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getMatterUrl(`/combox`),
    params
  })
  return res && res.data
}

// 事项的驳回原因
export const getRejectReasonApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getMatterUrl(`/getRejectReason/${id}`)
  })
  return res && res.data
}
