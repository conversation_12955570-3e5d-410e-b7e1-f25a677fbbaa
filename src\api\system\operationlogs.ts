import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { OperationLogsType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/szxcylog/business-logs'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取操作日志管理列表
export const getOperationLogsListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<OperationLogsType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}
