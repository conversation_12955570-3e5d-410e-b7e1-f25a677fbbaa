import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { VisitRecordType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/visitInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取走访记录列表
export const getVisitListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<VisitRecordType>>>({
    url: getUrl(),
    params
  })

  return res && res.data
}

//获取走访记录详情
export const getVisitDetailApi = async (id: string) => {
  const res = await request.get<IResponse<VisitRecordType>>({
    url: getUrl(id)
  })

  return res && res.data
}
