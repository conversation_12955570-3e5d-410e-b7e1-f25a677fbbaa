import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { GatewayLogsType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/gateway/log'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取操作日志管理列表
export const getGatewayLogsListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<GatewayLogsType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}
