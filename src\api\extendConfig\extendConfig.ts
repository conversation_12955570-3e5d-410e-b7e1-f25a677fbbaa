import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ObjectType, ExtendConfigType } from './types'
import { deepClone } from '@/utils/vform/util'

const request = useAxios()

// 基础路径
const BASE_URL_ORG = '/system/extendable-configs'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_ORG, path)
}

// 获取可配置数据
export const getObjectTypeListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ObjectType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 添加可扩展配置属性
export const addExtendConfigApi = async (data: ExtendConfigType) => {
  const res = await request.post<IResponse<ExtendConfigType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 修改可扩展配置属性
export const updateExtendConfigApi = async (data: Partial<ObjectType>) => {
  const params = deepClone(data)
  delete params.id
  const res = await request.put<IResponse<ObjectType>>({
    url: getUrl(data.id),
    data: params
  })
  return res && res.data
}

// 清除可扩展配置属性
export const delExtendConfigApi = async (id: string[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id[0])
  })
  return res && res.data
}

// 根据id查询分类
export const getExtendConfigDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`${id}`)
  })
  return res && res.data
}

// 根据permissionId查询分类
export const getPermissionDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`permission/${id}`)
  })
  return res && res.data
}
