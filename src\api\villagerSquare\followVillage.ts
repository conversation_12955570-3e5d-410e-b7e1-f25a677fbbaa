/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-03-20 16:36:13
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/userFollow'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取用户关注村子列表
export const getUserVillageFollowsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/userVillageFollowsList'),
    params
  })
  return res && res.data
}

// 删除选中用户关注村子
export const delUserVillageFollowApi = async (ids: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/deleteUserVillageFollow/${ids}`)
  })
  return res && res.data
}
