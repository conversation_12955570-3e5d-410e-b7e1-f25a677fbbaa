/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-09-04 10:09:12
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-21 16:28:04
 * @FilePath: \szxcy-gov-management\src\api\task\askPolicy.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { myTaskType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取我的待办列表
export const getMyTaskingListApi = async (params: Recordable) => {
  // params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<myTaskType>>>({
    url: getUrl('/task/unfinishedTasking'),
    params: {
      type: 1,
      ...params
    }
  })
  return res && res.data
}

// 我的待办审核详情及流程明细
export const myTaskingAudioDetailApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/task/taskingDetail/${params.id}`),
    params
  })

  return res && res.data
}

//  我的待办审核
export const audioTaskApi = async (params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/task/auditNewEvent/${params.id}`),
    params: {
      approveType: params.approveType,
      reason: params.reason
    }
  })

  return res && res.data
}

// 获取我的已办列表
export const getMyTaskedListApi = async (params: Recordable) => {
  // params.category = 'article_approval'
  const res = await request.get<IResponse<PageType<myTaskType>>>({
    url: getUrl('/task/finishedTasking'),
    params: {
      type: 1,
      ...params
    }
  })
  return res && res.data
}
