import { useAxios } from '@/hooks/web/useAxios'

const request = useAxios()
// 获取证明流程列表
export const getProcesList = async (params) => {
  const res = await request.get<IResponse>({
    url: '/attestationProcess',
    params
  })

  return res && res.data
}
// 刪除证明流程
export const delProces = async (id) => {
  const res = await request.delete<IResponse>({
    url: `/attestationProcess/${id}`
  })

  return res && res.data
}
// 更新证明流程
export const getAttestationProcessDetail = async (id) => {
  const res = await request.get<IResponse>({
    url: `/attestationProcess/${id}`
  })

  return res && res.data
}
// 更新证明流程状态
export const updateStatusProces = async (data) => {
  const res = await request.put<IResponse>({
    url: `/attestationProcess/updateStatus/${data.id}`,
    data
  })

  return res && res.data
}
// 创建证明流程
export const creatProces = async (data) => {
  const res = await request.post<IResponse>({
    url: `/attestationProcess`,
    data
  })

  return res && res.data
}
// 更新证明流程
export const updateProces = async (data) => {
  const res = await request.put<IResponse>({
    url: `/attestationProcess/${data.id}`,
    data
  })

  return res && res.data
}
// 证明流程设置流程id
export const updateProcesSetProcessId = async (data) => {
  const res = await request.put<IResponse>({
    url: `/attestationProcess/setProcessId/${data.id}`,
    data
  })

  return res && res.data
}
// 获取工作人员
export const getVillagerAffairsStaff = async (params) => {
  const res = await request.get<IResponse>({
    url: '/villagerAffairs/staff',
    params
  })

  return res && res.data
}
// 获取事项列表
export const getMatterInfoComboBox = async (params) => {
  const res = await request.get<IResponse>({
    url: '/villagerAffairs/matterInfo/comboBox',
    params
  })

  return res && res.data
}
