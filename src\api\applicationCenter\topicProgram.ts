/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-08-27 17:24:17
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-09-03 11:00:45
 * @FilePath: \szxcy-gov-management\src\api\applicationCenter\topicProgram.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { policyInfoDetailType } from './types'
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
const request = useAxios()

const BASE_URL = '/wenZhengYiTong/policyContentManagement'

//获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取政策信息列表
export const getPolicyInfoListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<policyInfoDetailType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

//添加政策信息
export const addPolicyInfoItemApi = async (data: Partial<policyInfoDetailType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

//修改政策信息
export const editPolicyInfoItemApi = async (data: Partial<policyInfoDetailType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${data.id}`),
    data
  })
  return res && res.data
}

// 政策信息详情
export const getPolicyInfoItemDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`),
    data: id
  })
  return res && res.data
}

// 删除单个政策信息
export const delPolicyInfoItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`),
    data: id
  })
  return res && res.data
}

//发起政策信息
export const startPolicyInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/initiateApproval/${data.id}`),
    data
  })
  return res && res.data
}

//政策信息  0-非置顶，1-置顶）
export const topOrNotPolicyInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateTopStatus/${data.id}`),
    params: {
      isTop: data.isTop
    }
  })
  return res && res.data
}

//政策信息（1-上架，2-下架）
export const upDownPolicyInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateShelves/${data.id}`),
    params: {
      status: data.status
    }
  })
  return res && res.data
}
