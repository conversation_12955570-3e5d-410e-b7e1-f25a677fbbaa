import { PermissionType } from '@/api/system/types'
import { config } from '@/config/axios/config'
import { MockMethod } from 'vite-plugin-mock'

const { result_code } = config

const timeout = 1000

const list: PermissionType[] = [
  {
    id: '1000',
    type: 1,
    code: '1000',
    title: '系统管理',
    name: 'System',
    component: '#',
    path: '/system',
    redirect: 'noredirect',
    icon: 'svg-icon:system',
    meta: { alwaysShow: true },
    parentId: '0',
    children: [
      {
        id: '1001',
        type: 1,
        code: '1001',
        title: '应用管理',
        name: 'Application',
        component: 'System/Application',
        path: 'application',
        redirect: 'noredirect',
        parentId: '1000'
      },
      {
        id: '1002',
        type: 1,
        code: '1002',
        title: '权限管理',
        name: 'Permission',
        component: 'System/Permission',
        path: 'permission',
        redirect: 'noredirect',
        parentId: '1000'
      },
      {
        id: '1003',
        type: 1,
        code: '1003',
        title: '字典管理',
        name: 'Dictionary',
        component: 'System/Dictionary',
        path: 'dict',
        redirect: 'noredirect',
        parentId: '1000'
      }
    ]
  }
]

export default [
  {
    url: '/api/permission',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: result_code,
        data: {
          totalElements: list.length,
          content: list
        }
      }
    }
  }
] as MockMethod[]
