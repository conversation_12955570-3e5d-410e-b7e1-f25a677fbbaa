import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { FormDataType } from './types'
import { Recordable } from 'vite-plugin-mock'

const request = useAxios()

// 基础路径
const BASE_URL_ORG = '/form/data'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL_ORG, path)
}

// 获取表单数据列表
export const getFormDataListApi = async (params?: Recordable) => {
  const data = { ...params }
  delete data.tableCode
  delete data.size
  delete data.page
  const res = await request.post<IResponse<PageType<FormDataType>>>({
    url: getUrl(`list/${params!.tableCode}?page=${params!.page}&size=${params!.size}`),
    data: data.query
  })
  return res && res.data
}

// 添加表单数据
export const addFormDataApi = async (data: FormDataType, code: string) => {
  const res = await request.post<IResponse<FormDataType>>({
    url: getUrl(code),
    data
  })
  return res && res.data
}

// 删除表单数据
export const delFormDataApi = async (id: string, code: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`${code}/${id}`)
  })
  return res && res.data
}

// 更新表单数据
export const updateFormDataApi = async (data: FormDataType, code: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`${code}`),
    data
  })
  return res && res.data
}

// 根据id查询数据
export const getFormDataDetailApi = async (id: string, code: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`${code}/${id}`)
  })
  return res && res.data
}
