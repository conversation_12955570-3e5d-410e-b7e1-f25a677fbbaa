import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { RoutesType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/gateway/routes'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取网关列表
export const getRoutesListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<RoutesType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取网关详情
export const getRoutesDetailApi = async (id: string) => {
  const res = await request.get<IResponse<RoutesType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 添加网关
export const addRoutesApi = async (data: RoutesType) => {
  const res = await request.post<IResponse<RoutesType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除网关
export const delRoutesApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新网关
export const updateRoutesApi = async (data: RoutesType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}
