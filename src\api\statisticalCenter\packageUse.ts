import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/statistic/package'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 套餐数据概览
export const getDataOverviewApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/dataOverview')
  })

  return res && res.data
}

// 套餐增长统计
export const getGrowthStatisticChartApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/growthStatisticChart'),
    params
  })

  return res && res.data
}

// 套餐统计表
export const getGrowthStatisticTableApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/growthStatisticTable'),
    params
  })

  return res && res.data
}

// 套餐统计表导出
export const growthStatisticTableExport = async (params: Recordable) => {
  const res = await request.get<any>({
    url: getUrl('/growthStatisticTable/export'),
    responseType: 'blob',
    params
  })

  return res && res.data
}
