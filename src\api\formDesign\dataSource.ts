import { useAxios } from '@/hooks/web/useAxiosDataSource'

const request = useAxios()

// 获取表单分类列表
export const getMethods = async (
  path: string,
  data?: Recordable,
  params?: Recordable,
  headers?: Recordable,
  errorHandlerCode?: string,
  dataHandlerCode?: string,
  configHandlerCode?: string
) => {
  const res = await request.get<IResponse<PageType<any>>>({
    url: path,
    params,
    data,
    headers,
    errorHandlerCode,
    dataHandlerCode,
    configHandlerCode
  })
  return res
}

export const postMethods = async (
  path: string,
  data?: Recordable,
  params?: Recordable,
  headers?: Recordable,
  errorHandlerCode?: string,
  dataHandlerCode?: string,
  configHandlerCode?: string
) => {
  const res = await request.post<IResponse<PageType<any>>>({
    url: path,
    data,
    params,
    headers,
    errorHandlerCode,
    dataHandlerCode,
    configHandlerCode
  })
  return res
}

export const putMethods = async (
  path: string,
  data?: Recordable,
  params?: Recordable,
  headers?: Recordable,
  errorHandlerCode?: string,
  dataHandlerCode?: string,
  configHandlerCode?: string
) => {
  const res = await request.put<IResponse<PageType<any>>>({
    url: path,
    data,
    params,
    headers,
    errorHandlerCode,
    dataHandlerCode,
    configHandlerCode
  })
  return res
}

export const deleteMethods = async (
  path: string,
  params?: Recordable,
  data?: Recordable,
  headers?: Recordable,
  errorHandlerCode?: string,
  dataHandlerCode?: string,
  configHandlerCode?: string
) => {
  const res = await request.delete<IResponse<PageType<any>>>({
    url: `${path}/${params!.id || data!.id}`,
    headers,
    errorHandlerCode,
    dataHandlerCode,
    configHandlerCode
  })
  return res
}

export const dataSourceApi = {
  getMethods,
  deleteMethods,
  postMethods,
  putMethods
}
