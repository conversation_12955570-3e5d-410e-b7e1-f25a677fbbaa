import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { WorkflowConfig } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/conf'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取模型表单配置列表
export const getWorkflowConfigApi = async (modelId: string) => {
  const res = await request.get<IResponse<WorkflowConfig>>({
    url: getUrl(modelId)
  })
  return res && res.data
}

// 更新表
export const updateWorkflowConfigApi = async (modelId: string, data: Partial<WorkflowConfig>) => {
  const res = await request.put<IResponse>({
    url: getUrl(modelId),
    data
  })
  return res && res.data
}
