import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
// import type { Type } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/devops'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 修改流程当前节点审批人
export const changeNodeApproveUserApi = async (taskId: string, userId: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`${taskId}/${userId}`)
  })
  return res && res.data
}

// 修改流程节点：驳回
export const changeNodeApi = async (taskId: string, targetKey: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`changeTask/${taskId}/${targetKey}`)
  })
  return res && res.data
}
