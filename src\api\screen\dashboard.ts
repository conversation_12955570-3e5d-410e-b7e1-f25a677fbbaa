import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { DashBoardType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/dashboard'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取接口列表
export const getDashBoardListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<DashBoardType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取接口详情
export const getDashBoardDetailApi = async (id: string) => {
  const res = await request.get<IResponse<DashBoardType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 添加接口
export const addDashBoardApi = async (data: Partial<DashBoardType>) => {
  const res = await request.post<IResponse<DashBoardType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除接口
export const delDashBoardApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(`${id}`)
  })
  return res && res.data
}

// 更新接口
export const updateDashBoardApi = async (data: Partial<DashBoardType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(`update/${data.id}`),
    data
  })
  return res && res.data
}

// 发布接口
export const publishDashBoardApi = async (data: Partial<DashBoardType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(`updatePublish/${data.id}`),
    data
  })
  return res && res.data
}

//设置挂载菜单
export const setPermissionMount = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`mount-dashboard?permissionId=${data.permissionId}&tblPageId=${data.id}`)
  })
  return res && res.data
}
