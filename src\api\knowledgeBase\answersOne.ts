import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { knowledgeAnswersOne } from './types'

const request = useAxios()

// 基础路径
// const BASE_URL = '/knowledgeAnswersOne'
const BASE_URL = '/knowledgeAnswersInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 查看详情-一问一答、文章知识
export const getDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`detail/${id}`)
  })
  return res && res.data
}

// 修改知识-一问一答、文章知识
export const editAnswersOneApi = async (data: Partial<knowledgeAnswersOne>) => {
  const res = await request.put<IResponse<knowledgeAnswersOne>>({
    url: getUrl(`edit`),
    data
  })
  return res && res.data
}

// 新增知识-一问一答、文章知识
export const addAnswersOneApi = async (data: Partial<knowledgeAnswersOne>) => {
  const res = await request.post<IResponse<knowledgeAnswersOne>>({
    url: getUrl(`add`),
    data
  })
  return res && res.data
}
