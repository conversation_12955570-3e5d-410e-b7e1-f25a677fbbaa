/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-10-16 16:22:45
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-11 18:12:12
 * @FilePath: \szxcy-gov-management-2\src\api\freshNews\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 频道信息
export interface channelDetailType {
  id?: string
  code?: string
  channelName: string
  channelDescription: string
  channelType: number
  followsNumber?: number
  newsNumber?: number
  status?: number
  createdUserName?: string
  lastModifiedTime?: string
}

// 话题信息
export interface topicDetailType {
  id?: string
  code?: string
  topicName: string
  topicIconType: number
  topicIcon: string
  topicDescripition: string
  topicHeat?: number
  status?: number
  createdUserName?: string
  createdTime?: string
  lastModifiedTime?: string
}

// 新闻标签
export interface newsTagType {
  id?: string
  code?: string
  tagName: string
  tagDescripition: string
  tagHeat?: string
  status?: string
  createdUserName?: string
  createdTime?: string
  lastModifiedTime?: string
}

// 新鲜事列表
export interface newEventType {
  id?: string
  code?: string
  orgCode: string
  orgName: string
  title: string
  dateType?: string
  topFlag?: string
  contentDetails?: string
  channelType?: string
  canVoice?: string
  tags?: Array<any>
  topics?: Array<any>
  authorName?: string
  status?: string
  creator?: string
  updatedTime?: string
  createdTime?: string
}

// 成长积分
export interface growthPointsType {
  id?: string
  code?: string
  orgCode: string
  orgName: string
  userName?: string
  status?: string
  pointsBalance?: string
  updatedTime?: string
  createdTime?: string
}

// 积分明细
export interface pointsDetailListType {
  id?: string
  code?: string
  orgCode: string
  orgName: string
  userName?: string
  userAction?: string
  pointChangeDetail?: string
  detail?: string
  createdTime?: string
  newEventCode?: string
}

// 收藏记录
export interface collectionType {
  id?: string
  code?: string
  orgCode: string
  orgName: string
  user?: string
  status?: string
  newEventId?: string
  newEventTitle?: string
  createdTime?: string
}

// banner设置
export interface bannerDetailType {
  id?: string
  sourceType: string
  newEventId?: string
  newEventTitle: string
  imgUrl: string
  articalUrl?: number
  sort?: string
  creator?: string
  updatedTime?: string
}

// 成长积分设置
export interface growthPointsSettingDetailType {
  id?: string
  userAction: string
  pointRuleDetail: string
  status: string
  lastModifiedTime?: number
}
