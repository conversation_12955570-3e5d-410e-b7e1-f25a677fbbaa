/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-08-20 15:10:41
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/supervise/task'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取督办管理待办结列表（待我审核的任务）
export const getPendingCompletionListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/pendingCompletion`),
    params
  })
  return res && res.data
}

// 获取督办任务列表（工作人员）
export const getTaskListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取督办任务详情
export const getTaskDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 督办任务办结审核
export const auditTaskApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/examine`),
    data
  })
  return res && res.data
}

// 督办任务签收
export const signForTaskApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/signFor/${id}`),
    data
  })
  return res && res.data
}

// 督办任务处理
export const handleTaskApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/completed/${id}`),
    data
  })
  return res && res.data
}
