import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type {
  DeployType,
  DraftListItemType,
  DraftType,
  ProcessCopyType,
  TaskDetailType,
  TaskType
} from './types'
import { isArray } from '@/utils/is'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/process'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取可发起的流程列表
export const getProcessListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<DeployType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取我的待办任务
export const getProcessTodoListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<TaskType>>>({
    url: getUrl('todos'),
    params
  })
  return res && res.data
}

// 获取我的已办任务
export const getProcessFinishListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<TaskType>>>({
    url: getUrl('finish'),
    params
  })
  return res && res.data
}

// 获取我发起的任务
export const getMineProcessListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<TaskType>>>({
    url: getUrl('own'),
    params
  })
  return res && res.data
}

// 获取流程当前节点form信息
export const getProcessFormApi = async (definitionId: string, deployId: string) => {
  const res = await request.get<
    IResponse<{
      fieldsPermission: string
      formConfVO: {
        deployId: string
        // formId: string
        procName: string
        formType: number
        formIdsAndVersion: string
      }
    }>
  >({
    url: getUrl(`${definitionId}/${deployId}`)
  })
  return res && res.data
}

// 启动流程
export const startProcessApi = async (
  processDefId: string,
  data: Recordable,
  processName: string | undefined = undefined
) => {
  const params: Recordable = {}
  if (processName) {
    params.processName = processName
  }
  const res = await request.post<IResponse>({
    url: getUrl(processDefId),
    data,
    params
  })
  return res && res.data
}

// 取消流程
export const cancelProcessApi = async (instanceId: string) => {
  const res = await request.post<IResponse>({
    url: getUrl(`cancel/${instanceId}`)
  })
  return res && res.data
}

// 获取当前流程的详细信息
export const getCurrentProcessDetailApi = async (processInsId: string, taskId: string) => {
  let url = processInsId
  if (taskId) {
    url += '/' + taskId
  }
  const res = await request.get<IResponse<TaskDetailType>>({
    url: getUrl(`detail/${url}`)
  })
  return res && res.data
}

// 获取我的抄送
export const getCopyProcessListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<ProcessCopyType>>>({
    url: getUrl('copyList'),
    params
  })
  return res && res.data
}

// 删除抄送
export const deleteCopyProcessApi = async (ids: string[]) => {
  const res = await request.post<IResponse>({
    url: getUrl('deleteCopy'),
    data: ids
  })
  return res && res.data
}
// 获取抄送的流程详情，此接口可将抄送状态设为已读
export const getCopyProcessDetailApi = async (processInsId: string) => {
  const res = await request.get<IResponse<TaskDetailType>>({
    url: getUrl(`copyDetail/${processInsId}`)
  })
  return res && res.data
}

// 获取我发起的流程草稿列表
export const getProcessDraftListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<DraftListItemType>>>({
    url: getUrl('getDraft'),
    params
  })
  return res && res.data
}

// 获取我发起的流程草稿列表
export const getProcessDraftDetailApi = async (draftId: string) => {
  const res = await request.get<IResponse<PageType<DraftType>>>({
    url: getUrl('getDraftById'),
    params: { draftId }
  })
  return res && res.data
}

// 获取流程暂存的详细信息(表单、审批意见等)
export const getDraftByProcInsIdApi = async (procInsId: string | undefined) => {
  if (procInsId) {
    const res = await request.get<IResponse<DraftType>>({
      url: getUrl('getDraftByProc'),
      params: { procInsId }
    })
    return res && res.data
  } else {
    return {
      data: undefined
    }
  }
}

// 保存草稿
export const saveProcessDraftApi = async (data: Partial<DraftType>) => {
  const res = await request.post<IResponse<any>>({
    url: getUrl('saveDraft'),
    data
  })
  return res && res.data
}

// 删除草稿
export const delProcessDraftApi = async (ids: string[] | string) => {
  const data: string[] = []
  if (isArray(ids)) {
    data.push(...ids)
  } else {
    data.push(ids)
  }
  const res = await request.post<IResponse<any>>({
    url: getUrl('deleteDraft'),
    data
  })
  return res && res.data
}

// 获取流程实例列表
export const getProcessInstanceListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<TaskType>>>({
    url: getUrl('all'),
    params
  })
  return res && res.data
}
