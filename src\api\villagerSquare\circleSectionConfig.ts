import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/ruralCircleColumnConfig'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取启用中的圈子板块列表
export const getEnableCircleListApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/enableColumn')
  })
  return res && res.data
}

// 获取未启用的圈子板块列表
export const getUnableCircleListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/unableColumn'),
    params
  })
  return res && res.data
}

// 获取“所属栏目板块”下拉框（PC端调用接口需设置type=1）
export const getColumnComboBoxApi = async () => {
  const res = await request.get<IResponse>({
    url: `/columnPlateConfig/comboBoxForPc?type=1`
  })
  return res && res.data
}

// 新增新增圈子板块
export const addCircleSectionItemApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑圈子板块
export const editCircleSectionItemApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`${id}`),
    data
  })
  return res && res.data
}

// 启用/停用圈子板块
export const onOffShelfApi = async (id: string, params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/onOffShelf/${id}`),
    params
  })
  return res && res.data
}

// 查看圈子板块详情
export const getCircleSectionDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`${id}`)
  })
  return res && res.data
}

// 查询栏目下启用的圈子板块数量（新建圈子板块用）
export const getCircleSectionNumApi = async (columnPlateId: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/columnLimitForFour/${columnPlateId}`)
  })
  return res && res.data
}
