/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-12-13 17:19:39
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-12-17 16:24:16
 * @FilePath: \szxcy-gov-management-2\src\api\screenManage\screenManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/provinceScreen'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取大屏模块信息
export const getModuleApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/module'),
    params
  })
  return res && res.data
}

//  模块重命名
export const renameModuleApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/module/rename/${data.id}`),
    params: { moduleName: data.moduleName }
  })
  return res && res.data
}

// 获取模块数据信 moduleId:模块id， moduleNumber为1 2 4 6 时调用此接口
export const getModuleContentApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getModuleContentData'),
    params
  })
  return res && res.data
}

//  更新模块数据信息（区县简介、基础信息、村级中间底图、网格信息）
export const updateModuleApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateModuleContentData`),
    data
  })
  return res && res.data
}

//  新增特色介绍/重点产业
export const addIntroductModuleApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/featureAndKeyIndustry`),
    data
  })
  return res && res.data
}

//  编辑特色介绍/重点产业（非禁用状态）
export const editIntroductModuleApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/featureAndKeyIndustry/${data.id}`),
    data
  })
  return res && res.data
}

// 查询详情-特色介绍/重点产业
export const getDetailIntroductModuleApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/featureAndKeyIndustry/${id}`)
  })
  return res && res.data
}

// 查询特色介绍/重点产业列表
export const getIntroductModuleListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/featureAndKeyIndustry'),
    params
  })
  return res && res.data
}

// 删除-特色介绍/重点产业（非禁用状态）
export const deleteIntroductModuleApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/featureAndKeyIndustry/${id}`)
  })
  return res && res.data
}

//  更新状态-特色介绍/重点产业
export const updateStatusIntroductModuleApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/featureAndKeyIndustry/updateStatus/${data.id}`),
    params: { status: data.status }
  })
  return res && res.data
}

//  新增快捷入口
export const addQuickEntryApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/quickEntry`),
    data
  })
  return res && res.data
}

//  编辑快捷入口（非禁用状态）
export const editQuickEntryApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/quickEntry/${data.id}`),
    data
  })
  return res && res.data
}

// 查询详情-快捷入口
export const getDetailQuickEntryApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/quickEntry/${id}`)
  })
  return res && res.data
}

// 查询快捷入口列表
export const getQuickEntryListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/quickEntry'),
    params
  })
  return res && res.data
}

// 删除-快捷入口（非禁用状态）
export const deleteQuickEntryApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/quickEntry/${id}`)
  })
  return res && res.data
}

//  更新状态-快捷入口
export const updateStatusQuickEntryApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/quickEntry/updateStatus/${data.id}`),
    params: { status: data.status }
  })
  return res && res.data
}
