import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { ActivityType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/statistic/business'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取功能活跃数
export const getActivityStatisticApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/activityStatistic')
  })

  return res && res.data
}

// 获取活跃数列表
export const getActivityStatisticListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<ActivityType>>>({
    url: getUrl('/activityStatisticByPage'),
    params
  })

  return res && res.data
}

// 导出活跃数列表
export const exportActivityStatisticListApi = async (params: Recordable) => {
  const res = await request.get<any>({
    url: getUrl('/export'),
    responseType: 'blob',
    params
  })

  return res && res.data
}
