/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-05-28 15:16:55
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-06-19 09:29:35
 * @FilePath: \szxcy-gov-management\src\api\gridManagement\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 走访记录
export interface VisitRecordType {
  id: string
  visitName: string
  type: string
  visitAddress: string
  gridId: string
  visitUserName: number
  visitTime: string
  visitPhotos: string
  content: string
  visitedName: string
}

// 事件处理
export interface EventProcessType {
  id: string
  eventName: string
  eventDescription: string
  eventType: string
  eventTime: string
  eventAddress: string
  eventPicture: string
  orgId: string
  gridId: string
  gridName: string
  createdUserId: string
  createdUserName: string
  createUserPhone: string
  createdTime: string
  eventStatus: string
  solveUserId: string
  solveUserName: string
  solveTime: string
  villagerEvaluate: string
  tenantId: string
}

// 工作任务处理
export interface WorkTaskType {
  id: string
  taskName: string
  visitUserId: string
  visitUserName: string
  visitUserType: number
  createTime: string
  endTime: string
  createUserId: string
  updateTime: string
  updateUserId: string
  photos: string
  remarks: string
  taskStatus: string
  orgcOde: string
  tenantId: string
  assignUser: Recordable
}

// 人口统计报表：网格村民
export interface GridVillagerType {
  id: string
  orgName: string
  gridName: string
  gridMemberName: string
  villagerNum: number
  orgCode: string
  gridId: string
}
