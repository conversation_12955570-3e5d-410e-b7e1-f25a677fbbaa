import { config } from '@/config/axios/config'
import { MockMethod } from 'vite-plugin-mock'
import type { ApplicationType } from '@/api/system/types'

const { result_code } = config

const timeout = 1000

const appList: ApplicationType[] = [
  {
    code: '10001',
    name: '默认应用',
    icon: 'http://xxxx',
    url: 'http://wwww.',
    orderNo: 0,
    createdTime: '2022-08-17T06:27:27.609Z',
    id: '1'
  }
]

export default [
  {
    url: '/api/application',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: result_code,
        data: {
          totalElements: appList.length,
          content: appList
        }
      }
    }
  },
  {
    url: '/api/application/:id',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const index = appList.findIndex((app) => app.id == query.id)
      if (index != -1) {
        appList.splice(index, 1)
        return {
          code: result_code,
          message: 'success'
        }
      } else {
        return {
          code: 400,
          message: '应用不存在'
        }
      }
    }
  },
  {
    url: '/api/application',
    method: 'post',
    timeout,
    response: ({ body }) => {
      body.createdTime = new Date()
      body.id = appList.length + 1
      appList.push(body as ApplicationType)
      return {
        code: result_code,
        message: 'success'
      }
    }
  },
  {
    url: '/api/application/:id',
    method: 'put',
    timeout,
    response: ({ body, query }) => {
      const id = query.id
      if (id) {
        const app = appList.find((app) => {
          console.log(app.id, id)
          return app.id == id
        })
        if (app) {
          Object.assign(app, body)
          return {
            code: result_code,
            message: 'success'
          }
        } else {
          return {
            code: 400,
            message: '应用不存在'
          }
        }
      } else {
        return {
          code: 400,
          message: '非法ID'
        }
      }
    }
  }
] as MockMethod[]
