import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent/approvalConfig'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取乡情圈的审核配置数据
export const getPublishExamineConfigDataApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl()
  })
  return res && res.data
}

// 编辑审核配置数据
export const editPublishExamineConfigApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}
