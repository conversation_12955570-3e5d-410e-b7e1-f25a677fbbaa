export interface StyleType {
  empty: boolean
  additionalProp1: object
  additionalProp2: object
  additionalProp3: object
}

export interface DashBoardType {
  name: string
  thumbnail: string
  isPublish: number
  canvasData: string
  canvasStyle: string
  creator: string
  createTime: string
  updater: string
  updateTime: string
  deleted: number
  tenantId: string
  id: string
  permissionId?: string
}

export interface DataSourceType {
  sourceCode: string
  sourceName: string
  sourceDesc?: string
  sourceType: string
  sourceConfig: string
  enableFlag?: number //0禁用 1启用
  deleted?: number
  tenantId?: string
  createdTime?: string
  lastModifiedTime?: string
  lastModifiedUserId?: string
  createdUserId?: string
  lastModifiedUserName?: string
  createdUserName?: string
  id?: string
}

export interface DataSetType {
  setCode: string
  setName: string
  setDesc: string
  sourceId: string
  dynSentence: string
  caseResult: string
  enableFlag: number
  deleted: number
  tenantId: string
  version: number
  setType: string
  createdTime: string
  lastModifiedTime: string
  lastModifiedUserId: string
  createdUserId: string
  lastModifiedUserName: string
  createdUserName: string
  id: string
}

export interface ParamsType {
  setId: string
  paramName: string
  paramDesc: string
  paramType: string
  sampleItem: string
}
