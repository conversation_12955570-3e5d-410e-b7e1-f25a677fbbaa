/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-08-02 15:22:34
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/examineConfig/examineConfigForPc'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取乡情圈的审核配置数据
export const getExamineConfigDataApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl()
  })
  return res && res.data
}

// 编辑审核配置数据
export const editExamineConfigApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}
