import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/statistic/user'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 用户数据概览
export const getDataOverviewApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/dataOverview')
  })

  return res && res.data
}

// 用户城市分布
export const getCityDistributionApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/cityDistribution'),
    params
  })

  return res && res.data
}

// 用户地图
export const getTenantMapApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/indicatorSummaryMap'),
    params
  })

  return res && res.data
}

// 用户增长统计
export const getGrowthStatisticChartApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/growthStatisticChart'),
    params
  })

  return res && res.data
}

// 用户活跃统计
export const getActivityStatisticChartApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/activityStatisticChart'),
    params
  })

  return res && res.data
}

// 用户统计表
export const getGrowthStatisticTableApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/growthStatisticTable'),
    params
  })

  return res && res.data
}

// 用户统计表导出
export const growthStatisticTableExport = async (params: Recordable) => {
  const res = await request.get<any>({
    url: getUrl('/growthStatisticTable/export'),
    responseType: 'blob',
    params
  })

  return res && res.data
}

// 用户数top5
export const getUserCountTop5Api = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/numberTop5Distribution'),
    params
  })

  return res && res.data
}

// 用户活跃top5
export const getUserActivityTop5Api = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/activityNumberTop5Distribution'),
    params
  })

  return res && res.data
}

// 性别分布
export const getGenderDistributionApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/genderDistribution'),
    params
  })

  return res && res.data
}
