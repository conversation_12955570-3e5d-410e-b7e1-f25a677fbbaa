import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { assessManageType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/assess'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取考核管理列表：全部、我发起的
export const getAssessListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessManageType>>>({
    url: getUrl('/management'),
    params
  })
  return res && res.data
}

// 获取考核管理列表：提交给我的
export const getSubmitToMeListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessManageType>>>({
    url: getUrl('/management/submitToMe'),
    params
  })
  return res && res.data
}

// 获取考核详情
export const getAssessDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/management/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取个人考核详情
export const getPeopleAssessDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/management/assessPeople/${id}`),
    params: { id }
  })
  return res && res.data
}

// 添加考核信息
export const addAssessInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/management`),
    data
  })
  return res && res.data
}

// 编辑考核信息
export const editAssessInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/management/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个考核数据
export const delAssessInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/management/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取考核详情-预览
export const getAssessPreviewApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/management/getPreviewByTemplateId/${id}`),
    params: { id }
  })
  return res && res.data
}

// 绑定流程: 设置考核流程（在流程暂存/发起时调用）
export const setAssessSetProcess = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/management/setProcessId`),
    data
  })

  return res && res.data
}

// 发布考核
export const publishAssessProcess = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/management/launchAssess/${data.id}`),
    data
  })

  return res && res.data
}

// 评价：审核流程接口6：审批
export const reviewProcessApprove = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/reviewProcess/approve',
    data
  })

  return res && res.data
}
