/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-10-31 14:28:25
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-10-31 14:32:49
 * @FilePath: \szxcy-gov-management\src\api\VillagersAffairs\department.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/villagerAffairs/departmentManagement'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取部门列表
export const getDepartmentListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增部门
export const addDepartmentApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑部门
export const editDepartmentApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 启用/禁用部门
export const changeDepartmentStatusApi = async (id: string, params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/switchOnOff/${id}`),
    params
  })
  return res && res.data
}

// 删除部门
export const delDepartmentApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 获取部门详情
export const getDepartmentDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 部门的下拉列表
export const getDepartmentDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/combox`),
    params
  })
  return res && res.data
}
