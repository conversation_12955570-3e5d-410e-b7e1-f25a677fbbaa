import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { pointType, villagerPointType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/score'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取积分管理列表
export const getScoreListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<pointType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增积分管理列表
export const addScoreApi = async (data: Partial<pointType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 修改积分管理列表
export const updateScoreApi = async (data: Partial<pointType>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateStatus/${id}`),
    data
  })
  return res && res.data
}

// 删除积分管理列表
export const deleteScoreApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 获取村民积分列表
export const getVillagerScoreListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<villagerPointType>>>({
    url: getUrl('/villagerInfo'),
    params
  })
  return res && res.data
}

// 设置村民积分列表
export const setVillagerScoreApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl('/villagerInfo'),
    data
  })
  return res && res.data
}

// 村民积分记录
export const villagerRecordApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<villagerPointType>>>({
    url: getUrl('/villagerInfoRecord'),
    params
  })
  return res && res.data
}

//村民积分记录撤销/批量撤销
export const cancelPointsApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl('/cancelRecord'),
    data: { ids: data }
  })
  return res && res.data
}

// 村民积分统计
export const pointStatisticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/statistics'),
    params
  })
  return res && res.data
}

// 推送积分记录
export const pushScoreApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/pushVillagerRecord`),
    data
  })
  return res && res.data
}

// 获取工作积分列表
export const getWorkScoreListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<villagerPointType>>>({
    url: getUrl('/workerInfo'),
    params
  })
  return res && res.data
}

// 设置工作积分列表
export const setWorkScoreApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/workerInfo/${data.id}`),
    data
  })
  return res && res.data
}

// 工作积分记录
export const workRecordApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<villagerPointType>>>({
    url: getUrl('/workerRecord'),
    params
  })
  return res && res.data
}

// 工作积分统计
export const workPointStatisticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/workerRecord/statistics'),
    params
  })
  return res && res.data
}

// 工作人员积分记录撤销/批量撤销
export const cancelRecordApi = async (data: Recordable) => {
  // console.log('---cancelRecordApi------', data)
  const res = await request.put<IResponse>({
    url: getUrl('/workerRecord/cancelRecord'),
    data: { ids: data }
  })
  return res && res.data
}
