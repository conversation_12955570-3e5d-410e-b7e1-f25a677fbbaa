import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { RoleType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/roles'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取角色列表
export const getRoleListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<RoleType>>>({
    url: '/roles/info',
    params
  })

  return res && res.data
}

// 超级管理员获取角色列表
export const adminGetRoleListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<RoleType>>>({
    url: '/roles/info/getAdminRoles',
    params
  })

  return res && res.data
}

//新增角色
export const addRoleApi = async (data: Partial<RoleType>) => {
  const res = await request.post<IResponse<RoleType>>({
    url: getUrl(),
    data
  })

  return res && res.data
}

//更新角色
export const updateRoleApi = async (data: Partial<RoleType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })

  return res && res.data
}

//删除角色
export const delRoleApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })

  return res && res.data
}

//根据id获取权限列表
export const infoRoleApi = async (id: string) => {
  const res = await request.get<IResponse<RoleType>>({
    url: getUrl(id)
  })

  return res && res.data
}

//设置角色下的权限
export const setRolePermissionApi = async (id: string, permissionIds: { id: string }[]) => {
  const res = await request.put<IResponse>({
    url: getUrl(id + '/permissions'),
    data: permissionIds
  })

  return res && res.data
}
// 获取角色详情
export const getRoleDetailApi = async (id: string) => {
  const res = await request.get<IResponse<RoleType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 修改角色状态
export const updateStatusApi = async (ids: string[], status: number) => {
  const res = await request.put<IResponse>({
    url: getUrl('/updateStatus'),
    data: { ids, status }
  })

  return res && res.data
}
// 更新数据权限
export const updateDataPermissionApi = async (
  id: string,
  data: {
    dataScope: number
    dataScopeOrganizationIds: string[]
    dataScopeDepartmentIds: string[]
  }
) => {
  const res = await request.put<IResponse>({
    url: getUrl(`${id}/updateDataPermission`),
    data
  })

  return res && res.data
}
//新增角色
export const importUser = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/roles/info/importUser',
    data
  })

  return res && res.data
}
