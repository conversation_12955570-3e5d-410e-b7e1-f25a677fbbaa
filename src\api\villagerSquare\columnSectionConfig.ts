/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-08-01 14:35:35
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/columnPlateConfig'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取栏目板块列表
export const getColumnSectionListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增栏目板块
export const addColumnSectionItemApi = async (data: Partial<Recordable>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 判断当前开启着的栏目板块是否已满4个
export const checkColumnPlateConfigApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/checkColumnPlateConfig`)
  })
  return res && res.data
}

// 编辑栏目板块
export const editColumnSectionItemApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`${id}`),
    data
  })
  return res && res.data
}

// 启用/停用栏目板块
export const onOffShelfApi = async (ids: string, params: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/onOffShelf/${ids}`),
    params
  })
  return res && res.data
}

// 查看栏目板块详情
export const getColumnSectionDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`${id}`)
  })
  return res && res.data
}
