import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ApiType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/apis'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取接口列表
export const getApiListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ApiType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取接口详情
export const getApiDetailApi = async (id: string) => {
  const res = await request.get<IResponse<ApiType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 添加接口
export const addApiApi = async (data: ApiType) => {
  const res = await request.post<IResponse<ApiType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除接口
export const delApiApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新接口
export const updateApiApi = async (data: ApiType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 获取tags
export const getApiTagsApi = async () => {
  const res = await request.get<IResponse<{ name?: string; description?: string }[]>>({
    url: getUrl('tags')
  })
  return res && res.data
}

// 根据swaggerDocUrl获取tag
export const getApiTagWithDocUrlApi = async (apiDocUrl: string) => {
  const res = await request.get<IResponse<{ name: string; description: string }[]>>({
    url: getUrl('doc/tags'),
    params: { apiDocUrl }
  })
  return res && res.data
}

// 导入api
export const importApiWithResAndTagApi = async (
  apiDocUrl: string,
  tagName: string,
  resourceId: string
) => {
  const res = await request.put<IResponse>({
    url: getUrl(resourceId + '/generated'),
    params: { apiDocUrl, tagName, resourceId }
  })
  return res && res.data
}
