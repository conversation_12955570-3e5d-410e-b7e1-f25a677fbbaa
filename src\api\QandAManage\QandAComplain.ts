import { useAxios } from '@/hooks/web/useAxios'
import type {
  acceptComplaintBodyType,
  complaintDetailType,
  complaintFormType,
  complaintQueryType,
  complaintResType
} from './types'

const request = useAxios()

/// 问答投诉-列表
export const getComplaintListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<complaintQueryType>>>({
    url: '/baKnowledgeBase/complaint',
    params
  })
  return res && res.data
}

export const addComplaintListApi = async (data: Partial<complaintFormType>) => {
  const res = await request.post<IResponse<complaintResType>>({
    url: '/baKnowledgeBase/complaint',
    data
  })
  return res && res.data
}

export const getComplaintDetailApi = async (id: string | string[]) => {
  const res = await request.get<IResponse<complaintDetailType>>({
    url: `/baKnowledgeBase/complaint/${id}`
  })
  return res && res.data
}

export const acceptComplaintApi = async (id: string, data: Partial<acceptComplaintBodyType>) => {
  const res = await request.put<IResponse>({
    url: `/baKnowledgeBase/complaint/${id}`,
    data
  })
  return res && res.data
}
