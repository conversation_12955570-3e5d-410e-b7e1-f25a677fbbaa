/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-07-11 10:48:30
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-08-07 11:24:18
 * @FilePath: \szxcy-gov-management\src\api\villagerSquare\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface momentList {
  momentContent?: string
  orgCode?: string
  orgName?: string
  status?: number
  momentPic?: string
  momentPicArr?: Array<string>
  topFlag?: number
  sort?: number
  shelves?: number
  createUserId?: string
  createTime?: string
  updateUserId?: string
  updateUserName?: string
  rejectReason?: string
  views?: number
  delFlag?: number
  approveId?: number
  approveName?: string
  createUserName?: string
  tenantId?: string
  id?: number
  startDate?: string
  endDate?: string
  isLike?: number
  like?: number
  likeNum?: number
  commentNum?: number
}

export interface commentList {
  momentId?: number
  content?: string
  createUserId?: string
  createUserName?: string
  orgCode?: string
  orgName?: string
  createTime?: string
  topFlag?: number
  sort?: number
  tenantId?: string
  delFlag?: number
  id?: number
}

// 栏目板块配置列表
export interface columnSectionListType {
  id?: string
  columnPlateOrder: number
  columnPlateName: string
  columnPlateDescription: string
  columnPlateStatus: number
  updatedTime?: string
  createdUser?: string
}

// 乡情圈列表
export interface ruralCircleListType {
  contentNumber: string
  orgName: string
  contentTitle: string
  contentStatus: number
  columnPlateName: string
  columnName: string
  createdUser: string
  createUserName: string
  contentTopFlag: number
  updatedTime: string
}

// AI能力配置列表
export interface aiListType {
  id?: string
  orgCode: string
  orgName: string
  appId: string
  appKey: string
  configStatus?: number
  updatedTime?: string
}
