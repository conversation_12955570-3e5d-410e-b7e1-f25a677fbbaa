/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-03-19 15:45:53
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-11-27 11:23:24
 * @FilePath: \szxcy-gov-management\src\api\system\message.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

const BASE_URL = '/notifications'

const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

export const getNotificationsListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/notification/notifications',
    //getUrl()
    params
  })

  return res && res.data
}

export const deleteNotificationsApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}

export const addNotificationsApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: `/notification/notifications`,
    //getUrl(),
    data
  })

  return res && res.data
}

export const updateNotificationsApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: `/notification/notifications/${data.id}`,
    // getUrl(`/${data.id}`),
    data
  })

  return res && res.data
}

export const loadItemNotificationsApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}

export const loadUserNotificationsApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: `/notification/notifications/personal`,
    //getUrl(`/personal`),
    params
  })

  return res && res.data
}
export const listReminderByPage = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: `/warmReminder/listReminderByPage`,
    //getUrl(`/personal`),
    params
  })

  return res && res.data
}

// 判断考核是否可评价
export const assessIsPraised = async (id: any, params: any) => {
  const res = await request.get<IResponse>({
    url: `/assess/management/assessPeople/isAllowJumps/${id}`,
    params
  })
  return res && res.data
}

// 判断乡情圈发布内容是否可审核
export const myVillageCircleUnfinishedTaskingIsPraised = async (id: any, messageId: any) => {
  const res = await request.get<IResponse>({
    url: `/villageCircleTask/myVillageCircleUnfinishedTaskingStatus/${id}?messageId=${messageId}`
  })
  return res && res.data
}

// 判断新鲜事儿是否需要跳转（审核和驳回编辑）
export const newsIsJump = async (params: any) => {
  const res = await request.get<IResponse>({
    url: `/newEvent/task/getMessageJumpInfo`,
    params
  })
  return res && res.data
}
