/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-09-03 16:59:11
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/supervise/type'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取督办类型列表
export const getSupervisionTypeListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增督办类型
export const addSupervisionTypeItemApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 编辑督办类型
export const editSupervisionTypeItemApi = async (data: Recordable, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 启用/禁用督办类型
export const changeSupervisionTypeStatusApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/typeStatus/${id}`)
  })
  return res && res.data
}

// 删除督办类型
export const delUserVillageFollowApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 督办类型获取编码
export const getTypeCodeApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getTypeCode`)
  })
  return res && res.data
}
