import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { collectionType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取频道列表
export const getCollectionRecordListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<collectionType>>>({
    url: getUrl(`/favorite/log`),
    params
  })
  return res && res.data
}

export const collectionRecordExcelTemple = async (params: any) => {
  const res = await request.get<any>({
    url: `/newEvent/favorite/log/exportExcel`,
    responseType: 'blob',
    params
  })
  return res
}
