import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { WorkflowType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/workflow/model'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取工作流模型列表
export const getWorkflowModelListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<WorkflowType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增模型信息
export const addWorkflowModelApi = async (data: Partial<WorkflowType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 更新模型信息
export const updateWorkflowModelApi = async (data: Partial<WorkflowType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 保存模型xml文件
export const saveWorkflowModelBpmnXmlApi = async (
  modelId: string,
  bpmnXml: string,
  newVersion: boolean
) => {
  const res = await request.post<IResponse>({
    url: getUrl('save'),
    data: {
      modelId,
      bpmnXml,
      newVersion
    }
  })
  return res && res.data
}

// 获取模型xml信息
export const getWorkflowModelBpmnXmlApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`bpmnXml/${id}`)
  })
  return res && res.data
}

// 设置最新流程模型
export const setWorkflowModelLatestApi = async (modelId: string) => {
  const res = await request.post<IResponse>({
    url: getUrl('latest'),
    data: { modelId: modelId }
  })
  return res && res.data
}

// 删除模型
export const delWorkflowModelApi = async (id: string[] | string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id.toString())
  })
  return res && res.data
}

// 获取模型详情
export const getWorkflowModelDetailApi = async (id: string) => {
  const res = await request.get<IResponse<WorkflowType>>({
    url: getUrl(id)
  })
  return res && res.data
}

// 部署流程模型
export const deployWorkflowModelApi = async (modelId: string) => {
  const res = await request.post<IResponse>({
    url: getUrl(`deploy/${modelId}`)
  })
  return res && res.data
}
