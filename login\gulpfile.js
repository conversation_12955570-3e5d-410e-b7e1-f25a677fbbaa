const gulp = require('gulp')
const plugins = require('gulp-load-plugins')()
const proxy = require('http-proxy-middleware')
const htmlReplace = require('gulp-html-replace')
const fileinclude = require('gulp-file-include')
const htmlmin = require('gulp-htmlmin')
const open = require('open')

const app = {
  srcPath: 'src/',
  prdPath: 'dist/' // 生产打包
}

const JS_LIB = [
  app.srcPath + 'js/jsencrypt.min.js',
  app.srcPath + 'js/pwd.js',
  app.srcPath + 'js/index.js',
  app.srcPath + 'js/step.js',
  app.srcPath + 'js/disableDevtools.js'
]
const CSS_INDEX = [app.srcPath + 'css/index.css', app.srcPath + 'css/step.css']
const CSS_LIB = [app.srcPath + 'css/error.css']

function js(cb) {
  const options = {
    compress: {
      drop_console: true
    },
    output: {
      beautify: false
    }
  }
  gulp
    .src(JS_LIB)
    .pipe(plugins.concat('index.js'))
    .pipe(plugins.uglify(options))
    .pipe(gulp.dest(app.prdPath + 'js'))
    .pipe(plugins.connect.reload())

  gulp
    .src(app.srcPath + 'js/page/*.js')
    .pipe(plugins.uglify(options))
    .pipe(gulp.dest(app.prdPath + 'js'))
    .pipe(plugins.connect.reload())
  cb()
}

function css(cb) {
  gulp
    .src(CSS_LIB)
    .pipe(plugins.cssmin())
    .pipe(gulp.dest(app.prdPath + 'css'))
    .pipe(plugins.connect.reload())
  gulp
    .src(CSS_INDEX)
    .pipe(plugins.concat('index.css'))
    .pipe(plugins.cssmin())
    .pipe(gulp.dest(app.prdPath + 'css'))
    .pipe(plugins.connect.reload())
  cb()
}

function copy() {
  return gulp
    .src([app.srcPath + '**/*.html'], { base: app.srcPath })
    .pipe(
      fileinclude({
        prefix: '@@',
        basepath: '@file'
      })
    )
    .pipe(
      htmlReplace({
        indexjs: '/js/index.js?t=' + Date.now(),
        indexcss: '/css/index.css?t=' + Date.now(),
        errorcss: '/css/error.css?t=' + Date.now(),
        loginjs: '/js/login.js?t=' + Date.now(),
        double_authenticationjs: '/js/double_authentication.js?t=' + Date.now(),
        user_registerjs: '/js/user_register.js?t=' + Date.now(),
        password_forgetjs: '/js/password_forget.js?t=' + Date.now(),
        password_updatejs: '/js/password_update.js?t=' + Date.now(),
        password_expirejs: '/js/password_expire.js?t=' + Date.now(),
        sso_logoutjs: '/js/sso_logout.js?t=' + Date.now()
      })
    )
    .pipe(
      htmlmin({
        // 通过配置的参数进行压缩
        collapseWhitespace: true, //移出空格
        removeComments: true, // 删除注释
        // removeEmptyAttributes: true, //表示移出空的属性（仅限于原生属性）
        // collapseBooleanAttributes: true, // 移出布尔值属性
        // removeAttributeQuotes: true, // 移出属性上的双引号
        minifyCSS: true, //压缩内嵌式css代码（只能基本压缩，不能添加前缀）
        minifyJS: true // 压缩内嵌式js代码（只能基本压缩，不能进行转码）
        // removeStyleLinkTypeAttributes: true, //移出style和link标签上的type属性
        // removeScriptTypeAttributes: true // 移出script标签上默认的type属性
      })
    )
    .pipe(gulp.dest(app.prdPath))
}

function copyImg() {
  return gulp
    .src([app.srcPath + 'image/**/*.*'], { base: app.srcPath })
    .pipe(gulp.dest(app.prdPath))
}

function clean() {
  return gulp.src(app.prdPath, { allowEmpty: true, read: false }).pipe(plugins.clean())
}

function serve(cb) {
  const HOST = '0.0.0.0' // 监听IP
  const PORT = 8081 // 启动服务端口
  const PROXY_TARGET = 'http://**************:9080/' // 代理后端地址

  plugins.connect.server(
    {
      //启动一个服务器
      root: [app.prdPath], // 服务器从哪个路径开始读取，默认从开发路径读取
      livereload: true, // 自动刷新
      port: PORT,
      host: HOST,
      fallback: '/loginPage',
      middleware: function (connect, opt) {
        return [
          proxy.createProxyMiddleware(
            [
              '/loginPage',
              '/registerPage',
              '/passwordForgetPage',
              '/passwordUpdatePage',
              '/doubleAuthentication'
            ],
            {
              target: 'http://' + HOST + ':' + PORT + '/',
              pathRewrite: {
                '/registerPage': '/user_register.html',
                '/loginPage': '/login.html',
                // 研发云扫描会扫描到这里key不能还有密/码单词，开发时可以放开
                '/passwordForgetPage': '/password_forget.html', //忘记密码页
                '/passwordUpdatePage': '/password_update.html', // 修改密码页
                '/doubleAuthentication': '/double_authentication.html' // 二次验证
              }
            }
          ),
          proxy.createProxyMiddleware(
            [
              '/checkUserName', // 检测用户名是否存在(已停用)
              '/registerEmailCode', // 获取注册时邮箱验证码
              '/registerInfoCheck', // 检测用户注册信息
              '/register',
              '/loginByUserName', //登录
              '/esurfing/unifyAccount/getLoginUrl', // 获取扫码登录信息

              '/passwordForgetCaptcha', // 忘记密码,图片验证码
              '/passwordForgetObtainEmail', // 忘记密码,获取邮件地址
              '/passwordForgetEmailCode', // 忘记密码,获取邮箱验证码
              '/passwordForgetEmailCodeCheck', // 忘记密码,邮箱验证码验证
              '/passwordForgetUpdate', // 忘记密码,更新密码
              '/captcha.jpg',
              '/changePwd',

              '/esurfing/unifyAccount/getWapLoginUrl' //获取免密登录地址
            ],
            {
              target: PROXY_TARGET,
              changeOrigin: false,
              xfwd: false
            }
          )
        ]
      }
    },
    function () {
      console.log('服务已经启动>...')

      // 打开页面
      open('http://127.0.0.1:8081/login.html')
    }
  )

  // 监听
  gulp.watch(app.srcPath + '**/*.js', js)
  gulp.watch(app.srcPath + '**/*.css', css)
  gulp.watch(app.srcPath + '**/*.html', copy)

  cb()
}

var build = gulp.series(clean, gulp.parallel(js, css), copyImg, copy)

exports.build = build

exports.default = gulp.series(build, serve)
