import { useAxios } from '@/hooks/web/useAxios'
import type { SoleWordType, NearWordType, sensitiveWordType } from './types'

const request = useAxios()

/// 专有词-列表
export const getSoleWordDictApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<SoleWordType>>>({
    url: '/wordsProperDic/listByPage',
    params
  })
  return res && res.data
}
// 专有词-新增
export const addSoleWordDictApi = async (data: Partial<SoleWordType>) => {
  const res = await request.post<IResponse>({
    url: '/wordsProperDic/add',
    data
  })
  return res && res.data
}
// 专有词-编辑
export const editSoleWordDictApi = async (data: Partial<SoleWordType>) => {
  const res = await request.post<IResponse>({
    url: '/wordsProperDic/edit',
    data
  })
  return res && res.data
}
// 专有词-删除
export const delSoleWordDictApi = async (params: Recordable) => {
  const res = await request.post<IResponse>({
    url: `/wordsProperDic/remove`,
    params
  })
  return res && res.data
}
/// 近义词-列表
export const getNearWordDictApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<NearWordType>>>({
    url: '/wordsSynonymsDic/listByPage',
    params
  })
  return res && res.data
}
// 近义词-新增
export const addNearWordDictApi = async (data: Partial<NearWordType>) => {
  const res = await request.post<IResponse>({
    url: '/wordsSynonymsDic/add',
    data
  })
  return res && res.data
}
// 近义词-编辑
export const editNearWordDictApi = async (data: Partial<NearWordType>) => {
  const res = await request.post<IResponse>({
    url: '/wordsSynonymsDic/edit',
    data
  })
  return res && res.data
}
// 近义词-删除
export const delNearWordDictApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    // url: `/wordsSynonymsDic/remove`,
    url: `/wordsSynonymsDic/${id}` // 4.22重新封装接口
  })
  return res && res.data
}
/// 敏感词-列表
export const getSenWordDictApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<sensitiveWordType>>>({
    url: '/baWordsSensitiveDic/listByPage',
    params
  })
  return res && res.data
}
// 敏感词-新增
export const addSenWordDictApi = async (data: Partial<sensitiveWordType>) => {
  const res = await request.post<IResponse>({
    url: '/baWordsSensitiveDic/add',
    data
  })
  return res && res.data
}
// 敏感词-编辑
export const editSenWordDictApi = async (data: Partial<sensitiveWordType>) => {
  const res = await request.post<IResponse>({
    url: '/baWordsSensitiveDic/edit',
    data
  })
  return res && res.data
}
// 敏感词-删除
export const delSenWordDictApi = async (params: Recordable) => {
  const res = await request.post<IResponse>({
    url: `/baWordsSensitiveDic/remove`,
    params
  })
  return res && res.data
}
