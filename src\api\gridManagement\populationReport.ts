/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-06-19 09:12:54
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-06-19 09:16:38
 * @FilePath: \szxcy-gov-management\src\api\gridManagement\populationReport.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { GridVillagerType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/populationStatistics'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 村民信息统计
export const getVillagerInfoApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/villagerInfo'),
    params
  })

  return res && res.data
}

//标签统计
export const getTagStatisticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/tagStatistics'),
    params
  })

  return res && res.data
}

//关联网格的村民统计
export const getGridVillagerStatisticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<GridVillagerType>>>({
    url: getUrl('/gridVillagerStatistics'),
    params
  })

  return res && res.data
}
