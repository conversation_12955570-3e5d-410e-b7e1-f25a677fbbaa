import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { knowledgeAnswersInfo } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/knowledgeAnswersInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 查询知识列表
export const getListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<knowledgeAnswersInfo>>>({
    url: getUrl('listByPage'),
    params
  })
  return res && res.data
}

// 待审核知识列表
export const getAuditListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<knowledgeAnswersInfo>>>({
    url: getUrl('auditPage'),
    params
  })
  return res && res.data
}

// 撤回
export const withdrawApi = async (id: string) => {
  const res = await request.post<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`withdraw/${id}`)
  })
  return res && res.data
}

// 提交
export const submitApi = async (id: string) => {
  const res = await request.post<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`submit/${id}`)
  })
  return res && res.data
}

// 批量审核知识
export const reviewApi = async (data: Partial<knowledgeAnswersInfo>) => {
  const res = await request.post<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`review`),
    data
  })
  return res && res.data
}

// 根据id删除知识
export const delByIdApi = async (id: string) => {
  const res = await request.delete<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`remove/${id}`)
  })
  return res && res.data
}

// 发布知识
export const releaseApi = async (id: string) => {
  const res = await request.post<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`release/${id}`)
  })
  return res && res.data
}

// 取消发布知识
export const cancelReleaseApi = async (id: string) => {
  const res = await request.post<IResponse<knowledgeAnswersInfo>>({
    url: getUrl(`cancelRelease/${id}`)
  })
  return res && res.data
}

// 生成近似问题
export const createQuestionsApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`createQuestions`),
    params
  })
  return res && res.data
}

// 批量导入
export const importListApi = async (params?: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl(`importList`),
    // headers: {
    //   'Content-Type': 'multipart/form-data'
    // },
    params
  })
  return res && res.data
}

// 关联标签
export const correlationLabelApi = async (data) => {
  const res = await request.post<IResponse>({
    url: getUrl('tagEdit'),
    data
  })
  return res && res.data
}

//获取标签
export const getTagApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/tag/tree',
    params
  })
  return res && res.data
}
