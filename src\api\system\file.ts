import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
// import axios from 'axios'
// import authCache from '@/api/auth/cache'
import { ElMessage } from 'element-plus'

const request = useAxios()

// 基础路径
const BASE_URL = '/file'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

/**
 * 上传文件，并且返回文件ID
 * @param file 文件
 * @param onUploadProgress 进度
 * @returns id:string
 */
export const fileUploadApi = async (
  file: File,
  onUploadProgress?: (progressEvent: ProgressEvent) => void
) => {
  if (file) {
    const res = await fileUploadWithUrlApi(file, onUploadProgress)
    if (res && res.data) {
      return res.data.id
    }
  }
  return ''
}

/**
 * 上传文件，返回id和url
 * @param file 文件
 * @param onUploadProgress 上传进度
 * @returns {id: string, url: string}
 */
export const fileUploadWithUrlApi = async (
  file: File,
  onUploadProgress?: (progressEvent: ProgressEvent) => void
) => {
  if (file) {
    const data = new FormData()
    data.set('file', file, file.name)
    const res = await request.post<IResponse<{ id: string; url: string }>>({
      url: getUrl('upload'),
      headersType: 'multipart/form-data',
      data,
      onUploadProgress
    })
    return res && res.data
  }
  return ''
}

// 根据ID获取文件下载链接
export const getFileUrlApi = async (id: string) => {
  const res = await request.get<IResponse<string>>({
    url: getUrl('getUrl/' + id)
  })
  return res && res.data
}

// 根据ID获取文件下载链接
export const getFileUrlApiNew = async (id: string) => {
  const res = await request.get<IResponse<string>>({
    url: `/agrTech/getUrl/${id}`
  })
  return res && res.data
}

// 根据ID获取文件下载链接,链接有效时间为duration,单位秒
export const getFileUrlForDurationApi = async (id: string, duration = 3600) => {
  const res = await request.get<IResponse<string>>({
    url: getUrl('getUrlForDuration/' + id + '/' + duration)
  })
  return res && res.data
}

export const downFileApi = async (id: string, fileName: string) => {
  return request
    .get({
      url: `file/download/${id}`,
      headers: {
        accept: '*/*'
      },
      responseType: 'blob'
    })
    .then((res) => {
      if (res.data) {
        const blob = new Blob([res.data as any], { type: 'application/octet-stream' })
        const linkNode = document.createElement('a')
        linkNode.style.display = 'none'
        const contentDis = res.headers['content-disposition']
        const extName = contentDis.substring(contentDis.lastIndexOf('.', contentDis.length))
        linkNode.download = fileName + (fileName.includes(extName) ? '' : extName)
        linkNode.href =
          window.URL && window.URL.createObjectURL
            ? window.URL.createObjectURL(blob)
            : window.webkitURL.createObjectURL(blob)
        document.body.appendChild(linkNode)
        linkNode.click() //模拟在按钮上的一次鼠标单击

        setTimeout(function () {
          URL.revokeObjectURL(linkNode.href) // 释放URL 对象
          document.body.removeChild(linkNode)
        }, 200)
      } else {
        ElMessage.error('下载失败，请重新下载')
      }
    })
    .catch((error) => {
      ElMessage.error(error.message)
    })
}

export const downQAFileApi = async (id: number, fileName: string) => {
  return request
    .get({
      url: `downloadExcel?type=${id}`,
      headers: {
        accept: '*/*'
      },
      responseType: 'blob'
    })
    .then((res) => {
      if (res.data) {
        const blob = new Blob([res.data as any], { type: 'application/octet-stream' })
        const linkNode = document.createElement('a')
        linkNode.style.display = 'none'
        const contentDis = res.headers['content-disposition']
        const extName = contentDis.substring(
          contentDis.lastIndexOf('.', contentDis.length),
          contentDis.length - 1
        )
        console.log('extName', contentDis, extName)
        linkNode.download = fileName + (fileName.includes(extName) ? '' : extName)
        linkNode.href =
          window.URL && window.URL.createObjectURL
            ? window.URL.createObjectURL(blob)
            : window.webkitURL.createObjectURL(blob)
        document.body.appendChild(linkNode)
        linkNode.click() //模拟在按钮上的一次鼠标单击

        setTimeout(function () {
          URL.revokeObjectURL(linkNode.href) // 释放URL 对象
          document.body.removeChild(linkNode)
        }, 200)
      } else {
        ElMessage.error('下载失败，请重新下载')
      }
    })
    .catch((error) => {
      ElMessage.error(error.message)
    })
}
