import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import filedownload from '@/utils/filedownload'
import { AxiosResponse } from 'axios'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/excels'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

/**
 * 导出excel数据
 * @param type 导出数据类型：1-用户
 * @param ids 要导出的数据的ID
 * @param autoDownload 是否自动下载
 * @returns
 */
export const exportDatasApi = async (type: number, ids: string[], autoDownload = true) => {
  const res = await request.post<Blob>({
    url: getUrl('/export/' + type),
    responseType: 'blob',
    data: ids
  })
  autoDownload && download(res, '用户.xlsx')
  return res && res.data
}

/**
 * 导入excel数据
 * @param type 类型
 * @param file 文件
 * @returns
 */
export const importDatasApi = async (type: number, file: File) => {
  const data = new FormData()
  data.append('file', file, file.name)
  const res = await request.post<Blob>({
    url: getUrl('/import/' + type),
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
  return res && res.data
}

/**
 *
 * @param type 类型
 * @param ids
 * @param autoDownload
 * @returns
 */
export const downloadTemplateApi = async (type: number, autoDownload = true) => {
  const res = await request.get<Blob>({
    url: getUrl('/templates/' + type),
    responseType: 'blob'
  })
  autoDownload && download(res, 'template.xlsx')
  return res && res.data
}

const download = (res: AxiosResponse<Blob>, defaultFileName: string) => {
  if (res && res.data) {
    let fileName = defaultFileName
    if (
      res.headers['content-disposition'] &&
      res.headers['content-disposition'].indexOf('filename=')
    ) {
      fileName = res.headers['content-disposition'].split('filename=')[1]
      fileName = decodeURIComponent(fileName)
    }
    filedownload(res.data, fileName)
  }
}
