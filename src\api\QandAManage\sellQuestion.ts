import { useAxios } from '@/hooks/web/useAxios'
import type { sellQuesClassType, sellQuesType } from './types'

const request = useAxios()

/// 推荐分类-列表
export const getSellQuesClassApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<sellQuesClassType>>>({
    url: '/questionReClassify/listByPage',
    params
  })
  return res && res.data
}
// 推荐分类-新增
export const addSellQuesClassApi = async (data: Partial<sellQuesClassType>) => {
  const res = await request.post<IResponse>({
    // url: '/questionReClassify/add',
    url: '/questionReClassify',
    data
  })
  return res && res.data
}
// 推荐分类-编辑
export const editSellQuesClassApi = async (data: Partial<sellQuesClassType>) => {
  const res = await request.put<IResponse>({
    // url: '/questionReClassify/edit',
    url: '/questionReClassify',
    data
  })
  return res && res.data
}
// 推荐分类-删除
export const delSellQuesClassApi = async (data: Partial<sellQuesClassType>) => {
  const res = await request.delete<IResponse>({
    url: `/questionReClassify/${data.id}`
  })
  return res && res.data
}
/// 推荐问题-列表
export const getSellQuesApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<sellQuesType>>>({
    // url: '/questionRecommended/listByPage',
    url: '/questionRecommended',
    params
  })
  return res && res.data
}
// 推荐问题-新增
export const addSellQuesApi = async (data: Partial<sellQuesType>) => {
  const res = await request.post<IResponse>({
    // url: '/questionRecommended/add',
    url: '/questionRecommended',
    data
  })
  return res && res.data
}
// 推荐问题-编辑
export const editSellQuesApi = async (data: Partial<sellQuesType>) => {
  const res = await request.put<IResponse>({
    // url: '/questionRecommended/edit',
    url: '/questionRecommended',
    data
  })
  return res && res.data
}
// 推荐问题-删除
export const delSellQuesApi = async (params: Recordable) => {
  const res = await request.delete<IResponse>({
    // url: `/questionRecommended/removeBatch`,
    url: `/questionRecommended/${params.id}`
  })
  return res && res.data
}
// 推荐问题-启用禁用
export const modifySellQuesStatusApi = async (data: Partial<sellQuesType>) => {
  const res = await request.put<IResponse>({
    url: '/questionRecommended/modifyStatus',
    data
  })
  return res && res.data
}
