/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-03-26 10:23:24
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { updatePackageType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/package'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取套餐列表
export const getPackageListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

//添加套餐
export const addPackageItemApi = async (data: Partial<updatePackageType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

//删除套餐
export const delPackageItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 编辑套餐
export const editPackageItemApi = async (id: string, data: Partial<updatePackageType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(id),
    data
  })
  return res && res.data
}

// 获取套餐详情
export const getPackageDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 生成套餐编码
export const generatePackageCodeApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('/generatePackageCode')
  })
  return res && res.data
}

//根据套餐id查询权限列表
export const getPackagePermissionListApi = async (packageId: string) => {
  const res = await request.get<IResponse>({
    url: `/packagePermission/${packageId}`
  })
  return res && res.data
}

//更新套餐下的权限
export const setPackagePermissionApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: '/packagePermission',
    data
  })

  return res && res.data
}

// 新增租户时，根据所属区域编码查询能配置的套餐列表
export const getPackageListByOrgCodeApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getPackageListByOrgCode'),
    params
  })
  return res && res.data
}

// 套餐导出明细
export const exportPackageListApi = async (params?: Recordable) => {
  const res = await request.get<any>({
    url: getUrl('/export'),
    responseType: 'blob',
    params
  })
  return res
}
