import { useAxios } from '@/hooks/web/useAxios'
import { FormDataType } from '@/api/formDesign/types'

const request = useAxios()
// 获取所属
export const getTreeList = async () => {
  const res = await request.get<IResponse>({
    url: '/org/info/getTreeList'
  })

  return res && res.data
}
// 获取积分项
export const getScoreList = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/score',
    params
  })

  return res && res.data
}
// 获取巡检点
export const getInspectionPoint = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/inspectionPoint/list',
    params
  })

  return res && res.data
}
// 获取工作任务角色
export const processWorkRoles = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/roles/info/processWork',
    params
  })

  return res && res.data
}
// 获取工作任务人员
export const processWorkUser = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/users/user/processWork',
    params
  })

  return res && res.data
}
// 获取人员
export const getAllUser = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/users/user/personList',
    params
  })

  return res && res.data
}
// 获取网格列表
export const getGridInfoListByOrgId = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/gridInfo/getGridInfoListByOrgId',
    params
  })

  return res && res.data
}
// 获取表单
export const getFormTable = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/form/table',
    params
  })

  return res && res.data
}
// 创建任务
export const creatTask = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/processWork',
    data
  })

  return res && res.data
}
// 获取表单
export const getTaskEditDetail = async (id) => {
  const res = await request.get<IResponse>({
    url: `/processWork/${id}`
  })

  return res && res.data
}
// 修改任务
export const changeTask = async (data: any) => {
  const res = await request.put<IResponse>({
    url: '/processWork',
    data
  })

  return res && res.data
}
// 刪除任務
export const delTaskEditDetail = async (id) => {
  const res = await request.delete<IResponse>({
    url: `/processWork/${id}`
  })

  return res && res.data
}
// 获取任务详情
export const getTaskDetail = async (id) => {
  const res = await request.get<IResponse>({
    url: `/processWork/processDetial/${id}`
  })

  return res && res.data
}
// 获取工作情况
export const getTaskTable = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/workDetail',
    params
  })

  return res && res.data
}
// 获取工作提交记录
export const getTaskSubmitlog = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/workDetail/submitlog',
    params
  })

  return res && res.data
}
// 获取工作提交记录统计
export const getTaskSubmitlogStatistic = async (params: any) => {
  const res = await request.get<IResponse>({
    url: `/workDetail/submitlog/statistic`,
    params
  })

  return res && res.data
}
// 获取工作提交记录统计
export const getTaskPersonal = async (params: any) => {
  const res = await request.get<IResponse>({
    url: `/processWork/personal/${params.taskId}`,
    params
  })

  return res && res.data
}
// 获取巡检进度详情
export const getSubmitlogInspection = async (params: any) => {
  const res = await request.get<IResponse>({
    url: `/workDetail/submitlog/inspection`,
    params
  })

  return res && res.data
}
// 获取数据采集信息数据表头
export const getCollectionhead = async (params: any) => {
  const res = await request.get<any>({
    url: `/workDetail/collectionhead`,
    params
  })

  return res && res.data
}
// 获取数据采集信息数据
export const getCollectionData = async (params: any) => {
  const res = await request.get<any>({
    url: `/workDetail/collectionlog`,
    params
  })

  return res && res.data
}
// 获取工作統計数据
export const getTaskStatisticTaskAndSubmit = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/taskAndSubmit',
    params
  })

  return res && res.data
}
// 获取工作統計数据
export const getTaskStatisticMyTaskCount = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/myTaskCount ',
    params
  })

  return res && res.data
}
// 获取工作統計数据
export const getTaskStatisticTaskCountOfType = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/taskCountOfType',
    params
  })

  return res && res.data
}
// 获取网格列表
export const getStatisticRanking = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/ranking',
    params
  })

  return res && res.data
}
// 获取网格列表
export const getMyTaskCountOfMonth = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/taskCountOfMonth',
    params
  })

  return res && res.data
}
// 获取网格列表
export const getStatisticTaskCount = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/processWork/statistic/taskCount',
    params
  })

  return res && res.data
}
// 获取批量上传模板
export const getBatchAddTemplateApi = async (params: any) => {
  const res = await request.get<any>({
    url: `/processWork/collectionlog/excelTemple`,
    responseType: 'blob',
    params
  })
  return res
}
// @ts-ignore
export const exportGridListApi = async (data: Recordable) => {
  const res = await request.post<any>({
    responseType: 'blob',
    url: '/processWork/collectionlog/importExcel ',
    data
  })

  return res
}
// 添加表单数据
export const addFormDataApi = async (data: FormDataType, taskId: any) => {
  const res = await request.post<IResponse<FormDataType>>({
    url: `/processWork/submit/collectionlog/${taskId}`,
    data
  })
  return res && res.data
}
// 获取批量上传模板
export const collectionlogExcelTemple = async (params: any) => {
  const res = await request.post<any>({
    url: `/processWork/collectionLog/exportExcel`,
    responseType: 'blob',
    params
  })
  return res
}
// 添加表单数据
export const changeTaskStatus = async (taskId: any) => {
  const res = await request.put<IResponse<FormDataType>>({
    url: `/processWork/taskState/${taskId}`
  })
  return res && res.data
}
// 获取大屏信息
export const getProvinceScreenInfo = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/screen/provinceScreen',
    params
  })

  return res && res.data
}
