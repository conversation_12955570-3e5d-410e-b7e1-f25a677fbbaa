import { useAxios } from '@/hooks/web/useAxios'
import {
  askQueryType,
  answerResType,
  answerLogType,
  QandARecordType,
  QaAppraiseQueryType
} from './types'
// import { askSessionType } from './types'

const request = useAxios()

// const baseUrl = '/qaAnswer'
const baseUrl = '/qaRecord'

export const qaTalksLogAddApi = async () => {
  const res = await request.post<IResponse<string>>({
    url: '/qaTalksLog/add'
  })
  return res && res.data
}

export const qaAnswerAskApi = async (data: askQueryType) => {
  const res = await request.post<IResponse<answerResType>>({
    url: '/qaAnswer/ask',
    data
  })
  return res && res.data
}

export const qaTalksLogApi = async (params?: askQueryType) => {
  const res = await request.get<IResponse<PageType<answerLogType>>>({
    url: '/qaTalksLog/listByPage',
    params
  })
  return res && res.data
}

export const qaTalksRemoveBatchApi = async (data: string[]) => {
  const res = await request.post<IResponse>({
    url: '/qaTalksLog/removeBatch',
    data
  })
  return res && res.data
}

export const getQaListApi = async (sessionId: string) => {
  const res = await request.get<IResponse<QandARecordType[]>>({
    url: '/qaAnswer/getQaList',
    params: { sessionId }
  })
  return res && res.data
}

export const qaAnswerAppraiseApi = async (data: QaAppraiseQueryType) => {
  const res = await request.post<IResponse>({
    url: '/qaAnswer/appraise',
    data
  })
  return res && res.data
}

export const getArticleApi = async (knowledgeId: string) => {
  const res = await request.get<IResponse<string>>({
    url: '/qaAnswer/getArticle',
    params: { knowledgeId }
  })
  return res && res.data
}
// 查询问题记录列表
export const getQaAnswerListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<QandARecordType>>>({
    // url: '/qaAnswer/listByPage',
    url: baseUrl + '/listByPage',
    params
  })
  return res && res.data
}
// 查看详情
export const getQaAnswerDetailApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<QandARecordType>>>({
    // url: '/qaAnswer/getQuestionDetail',
    url: baseUrl + `/detail/${params!.questionId}`
  })
  return res && res.data
}
