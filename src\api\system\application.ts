import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { ApplicationType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/applications'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取应用列表
export const getAppListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<ApplicationType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取应用详情
export const getAppDetailApi = async (id: string) => {
  const res = await request.get<IResponse<ApplicationType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 添加应用
export const addAppApi = async (data: ApplicationType) => {
  const res = await request.post<IResponse<ApplicationType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除应用
export const delAppApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新应用
export const updateAppApi = async (app: ApplicationType) => {
  const res = await request.put<IResponse>({
    url: getUrl(app.id),
    data: app
  })
  return res && res.data
}
