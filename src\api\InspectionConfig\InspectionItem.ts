/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-06-11 11:32:28
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/inspectionItem'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取巡检项分页列表
export const getInspectionItemListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增巡检项
export const addInspectionItemApi = async (data: Partial<Recordable>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 查询巡检项详情
export const getInspectionItemDetailApi = async (id: string) => {
  const res = await request.get<IResponse<Recordable>>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 编辑巡检项
export const editInspectionItemApi = async (data: Partial<Recordable>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 修改状态
export const updateStatusApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateStatus/${id}`)
  })
  return res && res.data
}

// 删除巡检项
export const delInspectionItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}
