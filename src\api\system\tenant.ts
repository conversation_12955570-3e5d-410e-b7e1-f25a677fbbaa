import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { TenantAdminType, TenantType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/tenants/info'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取租户列表
export const getTenantListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<TenantType>>>({
    url: getUrl(),
    params
  })

  return res && res.data
}

//新增租户
export const addTenantApi = async (data: Partial<TenantType>) => {
  const res = await request.post<IResponse<TenantType>>({
    url: getUrl(),
    data
  })

  return res && res.data
}

//注册租户
export const registerTenantApi = async (data: {
  tenant: Partial<TenantType>
  admin: Partial<TenantAdminType>
}) => {
  const res = await request.post<IResponse<TenantType>>({
    url: getUrl(),
    data
  })

  return res && res.data
}

//更新租户
export const updateTenantApi = async (data: Partial<TenantType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })

  return res && res.data
}

//删除租户
export const delTenantApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: '/system/tenants/' + id
  })

  return res && res.data
}

//获取租户详情
export const getTenantDetailApi = async (id: string) => {
  const res = await request.get<IResponse<TenantType>>({
    url: getUrl(id)
  })

  return res && res.data
}

//根据租户id查询权限列表
export const permissionListByTenantApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl('permission/list')
  })

  return res && res.data
}

//设置租户权限
export const setTenantPermissionApi = async (id: string, permissionIds: { id: string }[]) => {
  const res = await request.put<IResponse>({
    url: getUrl(id + '/permission'),
    data: permissionIds
  })

  return res && res.data
}

// 导出租户列表
export const exportTenantApi = async (params: Recordable) => {
  const res = await request.get<any>({
    responseType: 'blob',
    url: getUrl('/export'),
    params
  })

  return res && res.data
}

// 重置租户权限
export const resetPermission = async () => {
  const res = await request.get<IResponse<TenantType>>({
    url: getUrl('/resetPermission')
  })
  return res && res.data
}
