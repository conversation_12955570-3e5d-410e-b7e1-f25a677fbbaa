/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-06-11 17:00:20
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/inspectionPoint'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取巡检点分页列表
export const getInspectionPointListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 新增巡检点
export const addInspectionPointApi = async (data: Partial<Recordable>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 查询巡检点详情
export const getInspectionPointDetailApi = async (id: string) => {
  const res = await request.get<IResponse<Recordable>>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

// 查询巡检项下拉列表数据
export const getInspectionItemDropdownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: `/inspectionItem/list`,
    params
  })
  return res && res.data
}

// 编辑巡检点
export const editInspectionPointApi = async (data: Partial<Recordable>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })
  return res && res.data
}

// 修改巡检点状态
export const updateStatusApi = async (id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/updateStatus/${id}`)
  })
  return res && res.data
}

// 删除巡检点
export const delInspectionPointApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}
