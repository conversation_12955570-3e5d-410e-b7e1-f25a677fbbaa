import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/gridBoard'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 网格树形结构
export const getGridTreeApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridTree'),
    params
  })
  return res && res.data
}

// 网格事件查询
export const getGridEventApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridEvent'),
    params
  })
  return res && res.data
}

// 网格看板
export const getGridBoardApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridBoard'),
    params
  })
  return res && res.data
}

//网格责任人
export const getGridMemberApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridMember'),
    params
  })
  return res && res.data
}

//网格数据看板查询
export const getGridDataBoardApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridDataBoard'),
    params
  })
  return res && res.data
}

//网格数据工作概况
export const getGridWorkApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridWork'),
    params
  })
  return res && res.data
}

//网格村民管理查询
export const getGridVillagerApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridVillager'),
    params
  })
  return res && res.data
}
