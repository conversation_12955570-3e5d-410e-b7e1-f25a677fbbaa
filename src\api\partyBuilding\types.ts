// 党组织
export interface OragnizationType {
  id: string
  orgCode: string
  partyName: string
  userId: string
  partyPhone: string
  partyAddress: string
  partyBriefIntroduction: string
  createUser?: string
  createTime?: string
  updateUser?: string
  updateTime?: string
  tenantId: string
  orgName: string
}

// 党建活动
export interface EventType {
  id: string
  orgCode: string
  activityName: string
  activityPublisherId: string
  activityPublisherName: string
  objectIds: string
  objectNames: string
  deadline: string
  status: string
  notes?: string
}

// 信息发布
export interface SendMessageType {
  id: string
  title: string
  authorId: string
  authorName: string
  orgCode: string
  orgName: string
  source: string
  content: string
  showPicture: string
  siteId: number
  siteName: string
  channelId: number
  channelName: string
  status: string
  publishTime: string
  visits: number
  createdTime: string
  createdUserId: string
  createdUserName: string
  updatedTime: string
  updatedUserId: string
  updatedUserName: string
  delFlag: number
  tenantId: string
  publishType: string
  portalFlag: number
  publishPort: string | string[]
}
