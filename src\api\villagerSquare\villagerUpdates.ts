import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { commentList, momentList } from '@/api/villagerSquare/types'

const request = useAxios()

const BASE_URL = '/moment'
const BASE_COMMENT_URL = '/comment'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

const getCommentUrl = (path?: string): string => {
  return resolveUrl(BASE_COMMENT_URL, path)
}

export const getVillageMomentsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<momentList>>>({
    url: getUrl('/villageMomentsList'),
    params
  })
  return res && res.data
}

export const updateVillageMomentsApi = async (data: Recordable) => {
  const res = await request.put<IResponse<PageType<momentList>>>({
    url: getUrl('/villageMoments'),
    data
  })
  return res && res.data
}

export const villageMomentForAppApi = async (id: number, params: Recordable) => {
  const res = await request.get<IResponse<PageType<momentList>>>({
    url: getUrl(`/villageMomentForApp/${id}`),
    params
  })
  return res && res.data
}

export const villageMomentCommentsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<commentList>>>({
    url: getCommentUrl(`/villageMomentCommentsList`),
    params
  })
  return res && res.data
}

export const villageMomentsLikeApi = async (id: any, isLikeOrCancel: number) => {
  const res = await request.post<IResponse<PageType<momentList>>>({
    url: getUrl(`/villageMomentsLike/${id}?isLikeOrCancel=${isLikeOrCancel}`)
  })
  return res && res.data
}
export const villageMomentCommentsAddApi = async (data: Recordable) => {
  const res = await request.post<IResponse<PageType<commentList>>>({
    url: getCommentUrl(`/villageMomentComments`),
    data
  })
  return res && res.data
}

export const villageMomentCommentsUpdateApi = async (data: Recordable) => {
  const res = await request.put<IResponse<PageType<commentList>>>({
    url: getCommentUrl(`/villageMomentComments`),
    data
  })
  return res && res.data
}
