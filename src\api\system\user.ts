import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { UserType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/users/user'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取用户列表
export const getUserListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<UserType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 添加用户
export const addUserApi = async (data: Partial<UserType>) => {
  const res = await request.post<IResponse<UserType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除用户
export const delUserApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: '/system/users/' + id
  })
  return res && res.data
}

// 更新用户
export const updateUserApi = async (data: Partial<UserType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 获取用户详情
export const getUserDetailApi = async (id: string) => {
  const res = await request.get<IResponse<UserType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 重置密码,默认短信通知
export const resetPasswordApi = async (ids: string[], type = 1) => {
  const res = await request.put<IResponse>({
    url: '/system/users/updatePassword',
    data: { ids, type }
  })

  return res && res.data
}

// 修改用户状态
export const updateStatusApi = async (ids: string[], status: number) => {
  const res = await request.put<IResponse>({
    url: '/system/users/updateStatus',
    data: { ids, status }
  })

  return res && res.data
}

// 解冻用户
export const unlockApi = async (name: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${name}/unlock`)
  })

  return res && res.data
}
