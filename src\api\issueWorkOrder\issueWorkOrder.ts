import { useAxios } from '@/hooks/web/useAxios'

const request = useAxios()
// 获取证明流程列表
export const getAttestationTicketsList = async (params) => {
  const res = await request.get<IResponse>({
    url: '/attestationTickets/listByPagePc',
    params
  })

  return res && res.data
}
// 获取证明流程详情
export const getAttestationTicketsDetail = async (id) => {
  const res = await request.get<IResponse>({
    url: `/attestationTickets/${id}`
  })

  return res && res.data
}
// 获取证明流程数量
export const getAttestationTicketsStatics = async (params) => {
  const res = await request.get<IResponse>({
    url: '/attestationTickets/getStatics',
    params
  })

  return res && res.data
}
// 获取打印记录
export const getAttestationRecordList = async (params) => {
  const res = await request.get<IResponse>({
    url: '/attestationRecord',
    params
  })

  return res && res.data
}
// 生成打印记录
export const postAttestationRecord = async (data) => {
  const res = await request.post<IResponse>({
    url: '/attestationRecord',
    data
  })

  return res && res.data
}
// 证明归档
export const updateAttestationTickets = async (data, id) => {
  const res = await request.put<IResponse>({
    url: '/attestationTickets/' + id,
    data
  })

  return res && res.data
}
