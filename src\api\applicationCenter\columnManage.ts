/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-08-27 17:24:17
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-08-29 09:19:10
 * @FilePath: \szxcy-gov-management\src\api\applicationCenter\columnManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { columnInfoDetailType } from './types'
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
const request = useAxios()

const BASE_URL = '/wenZhengYiTong/columnManagement'

//获取路径
const getUrl = (path?: string): string => {
  console.log(resolveUrl(BASE_URL, path))
  return resolveUrl(BASE_URL, path)
}

//获取栏目列表（表格）
export const getColumnInfoListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<columnInfoDetailType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

//增加栏目信息
export const addColumnInfoItemApi = async (data: Partial<columnInfoDetailType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })
  return res && res.data
}

//编辑栏目信息
export const editColumnInfoItemApi = async (data: Partial<columnInfoDetailType>) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${data.id}`),
    data
  })
  return res && res.data
}

// 获取栏目信息详情
export const getColumnInfoDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/${id}`)
  })
  return res && res.data
}

//删除栏目信息
export const delColumnInfoItemApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`),
    data: id
  })
  return res && res.data
}
