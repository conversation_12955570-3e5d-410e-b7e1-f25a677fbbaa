import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { SendMessageType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/article'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取文章列表
export const getArticleListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<SendMessageType>>>({
    url: getUrl('/getWebConsolePage'),
    params
  })
  return res && res.data
}

// 批量删除文章
export const batchDelArticleApi = async (ids: number[]) => {
  const res = await request.delete<IResponse>({
    url: getUrl('/deleteByIds'),
    data: ids
  })

  return res && res.data
}

// 新建
export const addArticleApi = async (data: Partial<SendMessageType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })

  return res && res.data
}

// 编辑
export const updateArticleApi = async (data: Partial<SendMessageType>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })

  return res && res.data
}

// 获取详情
export const getArticleDetailApi = async (id: string) => {
  const res = await request.get<IResponse<SendMessageType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 获取站点列表
export const getsiteListApi = async () => {
  const res = await request.get<IResponse>({
    url: '/webSite/getSiteList'
  })

  return res && res.data
}
