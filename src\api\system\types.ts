export interface ApplicationType {
  id?: string
  code: string
  name: string
  icon: string
  url: string
  orderNo: number
  createdTime?: string
  // lastModifiedTime?: string
  // lastModifiedUserId?: string
  // lastModifiedUserName?: string
  // createdUserId?: string
  // createdUserName?: string
}

export interface PermissionType {
  id?: string
  parentId: string | undefined
  name: string
  meta?: string | Recordable
  component: string
  type: number
  path: string
  redirect?: string
  code: string
  title: string
  icon?: string
  orderNo?: number
  children?: Partial<PermissionType>[]
  parentIds?: string[]
  applicationId?: string
  applicationName?: string
  apis?: Partial<ApiType>[]
  disabled?: boolean
}

export interface ResourceType {
  id?: string
  code: string
  name: string
  orderNo: number
  createdTime?: string
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
}
export interface ApiType {
  id?: string
  resourceId: string
  resourceName: string
  code: string
  name: string
  orderNo: number
  tag: string
  tagName?: string
  createdTime?: string
  // resourceCode: string
  url: string
  method: string
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
}

export interface DictionaryCatalogType {
  id?: string
  code: string
  name: string
  orderNo: number
  parentId?: string
  parentCode?: string
  parentName?: string
  children?: DictionaryCatalogType[]
  dictionaryItems?: DictionaryItemType[]
  parentIds?: string[]
  disabled?: boolean
}

export interface DictionaryItemType {
  id?: string
  code: string
  name: string
  status?: number
  orderNo: number
  catalogId?: string
  catalogCode?: string
  catalogName?: string
}

export interface GroupType {
  id?: string
  parentId?: string | undefined
  code: string
  name: string
  orderNo?: number
  children?: Partial<GroupType>[]
  organizationId: string
  organizationCode: string
  organizationName: string
  disabled?: boolean
  createdTime?: string
  // parentCode: string
  // parentName: string
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
}

export interface PositionType {
  id: string
  code: string
  name: string
  status: number
  orderNo?: number
  createdTime?: string
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
}

export interface UserType {
  id?: string
  name: string
  username: string
  password: string
  rePassword?: string
  mobile: string
  sex: number
  enabled?: boolean
  idCardNo: string
  birthday?: string
  type?: number
  status?: number
  loading?: boolean
  email?: string
  avatar?: string
  rootPath?: string
  accountNonExpired?: boolean
  credentialsNonExpired?: boolean
  accountNonLocked?: boolean
  department?: Partial<DepartmentsType>
  roles?: Partial<RoleType>[] | string[]
  groups?: Partial<GroupType>[] | string[]
  positions?: Partial<PositionType>[] | string[]
  organization?: Partial<OrganizationType>
  orderNo?: number
  permission?: Partial<PermissionType>
  createdTime?: string
  extendableValues?: Recordable<string, string>
  ifSuperAdmin?: boolean

  // 查询用户详情时字段
  organizationId?: string
  organizationCode?: string
  organizationName?: string
  departmentId?: string
  departmentCode?: string
  departmentName?: string
  extraOrganizations?: Partial<OrganizationType>[]
  extraDepartments?: Partial<DepartmentsType>[]
  // lastLoginIp: string
  // lastLoginTime: string
  // organizationId: string
  // organizationCode: string
  // organizationName: string
  // departmentCode: string
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
  tenant: Recordable<string, string>
}

export interface RoleType {
  id?: string
  code: string
  name: string
  // type: string
  status?: number
  loading?: boolean
  orderNo?: number
  permissions?: object[]
  applicationCode: string
  applicationId: string
  applicationName: string
  createdTime?: string
  dataScope: number
  dataScopeOrganizationIds: string[]
  dataScopeDepartmentIds: string[]
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
}
export interface OrganizationType {
  id?: string
  code: string
  name: string
  orderNo: number
  parentId?: string
  parentCode?: string
  parentName?: string
  children?: OrganizationType[]
  dictionaryItems?: DepartmentsType[]
  parentIds?: string[]
  disabled?: boolean
}

export interface DepartmentsType {
  id?: string
  code: string
  name: string
  status?: number
  orderNo: number
  organizationId?: string
  organizationCode?: string
  organizationName?: string
  parentId?: string
  parentIds?: string[]
  extendableValues?: Recordable<string, string>
  disabled?: boolean
  rootPath?: string
}

export interface TenantAdminType {
  realname: string
  username: string
  sex: number
  mobile: string
  password?: string
  rePassword?: string
}

export interface TenantType extends Partial<TenantAdminType> {
  id: string
  code: string
  name: string
  status: number
  licenceDate: string
  principal: string
  principalMobile: string
  areaCode: string
  areaName: string
  address: string
  orderNo: number
  permissions?: Partial<PermissionType>[]
  createdTime?: string
  packageId: any[]
  packageList: any[]
  // lastModifiedTime: string
  // lastModifiedUserId: string
  // lastModifiedUserName: string
  // createdUserId: string
  // createdUserName: string
  admin?: Partial<UserType>
}
export interface OperationLogsType {
  id: string
  title: string
  businessType: number
  operationType: number
  departmentName?: string
  organizationName?: string
}

export interface GatewayLogsType {
  id: string
  origin: string
  requestPath: number
  requestMethod: number
  ip?: string
  responseCode?: string
}

export interface LoginLogsType {
  id: string
  principal: string
  realIp: number
  loginMethod: number
  loginStatus?: string
  loginTime?: any
  loginTimeStr?: string
  failureReason?: string
}

export interface ConfigType {
  code: string
  name: string
  value: string
  type: number
  orderNo: 0
  createdTime?: string
  id?: string
}

// 组织部门树结构
export interface OrgDeptTreeDataType {
  id: string
  parentId: string
  orgId?: string
  label: string
  srcId: string
  type: string
  children?: OrgDeptTreeDataType[]
}

export interface ReportType {
  id: string
  code: string
  name: string
  note: string
  status: string
  type: string
  jsonStr: string
  apiUrl: string
  thumb: string
  apiMethod: string
  apiCode: string
  template: number
  viewCount: number
  createBy: string
  createName: string
  createTime: string
  updateBy: string
  updateName: string
  updateTime: string
  permissionId?: string
}
