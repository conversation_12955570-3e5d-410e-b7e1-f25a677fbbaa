import { useAxios } from '@/hooks/web/useAxios'
import { nearWordType, standardWordType } from './types'

const request = useAxios()

// 标准词-列表
export const getWordsStandardApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<standardWordType>>>({
    // url: '/wordsStandardDic/listByPage',
    url: '/wordsStandardDic', // 4.22重新封装接口
    params
  })
  return res && res.data
}
// 标准词-删除
export const removeWordsStandardApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    // url: '/wordsStandardDic/removeBatch',
    url: `/wordsStandardDic/${id}` // 4.22重新封装接口
  })
  return res
}

// 标准词-新增
export const addWordsStandardApi = async (data: standardWordType) => {
  const res = await request.post<IResponse>({
    // url: '/wordsStandardDic/add',
    url: '/wordsStandardDic', // 4.22重新封装接口
    data
  })
  return res
}

// 标准词的近义词-列表
export const getWordsSynonymsApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<nearWordType>>>({
    // url: '/wordsSynonymsDic/listByPage',
    url: '/wordsSynonymsDic', // 4.22重新封装接口
    params
  })
  return res && res.data
}

// 标准词的近义词-新增
export const addWordsSynonymsApi = async (data: nearWordType) => {
  const res = await request.post<IResponse>({
    // url: '/wordsSynonymsDic/add',
    url: '/wordsSynonymsDic', // 4.22重新封装接口
    data
  })
  return res
}
