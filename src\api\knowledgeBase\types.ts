export interface KnowledgeBaseType {
  id: string
  knowledgeBaseName: string
  knowledgeBaseType: string
  knowledgeBasePurpose: string
  tenantId: string
}

export interface ShareKnowledgeBaseType {
  id: string
  knowledgeBaseName: string
  knowledgeBaseType: string
  knowledgeBasePurpose: string
  sourceBaseId: string
}

export interface knowledgeAnswersInfo {
  id: string
  knowledgeBaseName: string
  knowledgeBaseType: string
  knowledgeBasePurpose: string
  reviewType: Number
  notes: string
  idList: Recordable[]
}

export interface knowledgeAnswersOne {
  id: string
  knowledgeBaseId: string
  standardQuestion: string
  standardAnswers: string
  questionList: Recordable[]
}

export interface LabelManageType {
  id?: string
  tagName: string
  tagTypeId: string
  tagTypeName: string
}

export interface LabelManageClassType {
  id?: string
  tagTypeName: string
}
