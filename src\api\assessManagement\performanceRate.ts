import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { performanceRateType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/assess'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取绩效等级列表
export const getRateListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<performanceRateType>>>({
    url: getUrl('/level'),
    params
  })
  return res && res.data
}

// 获取绩效等级详情
export const getRateDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/level/${id}`),
    params: { id }
  })
  return res && res.data
}

// 添加绩效等级信息
export const addRateInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/level'),
    data
  })
  return res && res.data
}

// 编辑绩效等级信息
export const editRateInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/level/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个绩效等级
export const delRateInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/level/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取绩效等级下拉框列表
export const getRateDropDownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<performanceRateType>>>({
    url: getUrl('/level/dropDownList'),
    params
  })
  return res && res.data
}
