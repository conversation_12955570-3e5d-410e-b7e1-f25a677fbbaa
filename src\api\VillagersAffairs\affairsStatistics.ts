/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-11-26 18:26:40
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-11-26 18:33:49
 * @FilePath: \szxcy-gov-management\src\api\VillagersAffairs\affairsStatistics.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/attestationTickets'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取顶部总览统计
export const getOverviewStaticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/overviewStatics`),
    params
  })
  return res && res.data
}

// 按周统计折线图
export const getWeekStaticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/weekStatics`),
    params
  })
  return res && res.data
}
// 按月份统计折线图
export const getMonthStaticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/monthStatics`),
    params
  })
  return res && res.data
}
// 获取饼图统计
export const getPieStaticsApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/pieStatics`),
    params
  })
  return res && res.data
}
