import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { growthPointsType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/newEvent'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取成长积分列表
export const getGrowthPointsListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<growthPointsType>>>({
    url: getUrl(`/growthPoints`),
    params
  })
  return res && res.data
}

// 启用\禁用单个成长积分
export const changeStatusGrowthPointsInfoApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl(`/growthPoints/updateStatus/${data.userId}`),
    params: { status: data.status }
  })
  return res && res.data
}

// 导出成长积分列表
export const growthPointsExcelTemple = async (params: any) => {
  const res = await request.get<any>({
    url: `/newEvent/growthPoints/exportExcel`,
    responseType: 'blob',
    params
  })
  return res
}

// 获取成长积分明细列表
export const getPointsDetailListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<growthPointsType>>>({
    url: getUrl(`/growthPoints/pointsLog`),
    params
  })
  return res && res.data
}

//导出积分明细列表
export const pointsDetailExcelTemple = async (params: any) => {
  const res = await request.get<any>({
    url: `/newEvent/growthPoints/pointsLog/exportExcel`,
    responseType: 'blob',
    params
  })
  return res
}

// 获取成长积分统计
export const getPointsStatisticApi = async () => {
  const res = await request.get<IResponse>({
    url: getUrl(`/growthPoints/pointsLog/statistics`)
    // params
  })
  return res && res.data
}
