/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-08-09 17:53:39
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-12-02 09:00:45
 * @FilePath: \szxcy-gov-management\src\api\assessManagement\label.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 考核指标：考核规则
export const assessRuleObj = {
  '1': '>',
  '2': '<',
  '3': '=',
  '4': '>=',
  '5': '<=',
  '6': '包含'
}

// 考核指标：关联事件
export const eventTypeObj = {
  // '0': '不关联',
  '1': '关联网格事件完成数量',
  '2': '关联网格事件处置率',
  '3': '关联网格事件评价满意度',
  '4': '关联任务处理达标率',
  '5': '关联任务处理完成数量',
  '6': '关联巡查任务达标率',
  '7': '关联巡检任务完成数量',
  '8': '关联日常任务达标率',
  '9': '关联日常任务完成数量',
  '10': '关联采集任务达标率',
  '11': '关联采集任务完成数量'
}

// 考核管理：考核状态
export const assessStatusObj = {
  '1': '草稿',
  '2': '未开始',
  '3': '进行中',
  '4': '已结束'
}

// 考核管理：个人考核状态
export const personalAssessStatusObj = {
  '1': '-',
  '2': '未开始',
  '3': '评价中',
  '4': '评价完成'
}

// 转换考核预览试卷表格数据
export const changeAssessPreviewArr = (arr: any = []) => {
  // console.log('转换考核预览试卷表格数据', arr)
  let newArr: any = []
  arr.map((item) => {
    for (let i = 0; i < item.metrics.length; i++) {
      item.metrics[i].weiduName = item.name
      item.metrics[i].weiduScore = item.score
      item.metrics[i].dimensionItemId = item.id
      item.metrics[i].assessResult = ''
      if (i === 0) {
        item.metrics[i].rowSpans = item.metrics.length
      }
    }
    // newArr = [...newArr, ...item.metrics]
    newArr = newArr.concat(item.metrics)
    // console.log('====', item.metrics, newArr)
  })
  return newArr
}

// 考核预览试卷表格数据:动态合并行
export const objectSpanMethod = ({ row, columnIndex }: any) => {
  // 第二列合并
  if (columnIndex === 1) {
    if (row.rowSpans) {
      return {
        rowspan: row.rowSpans,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}
