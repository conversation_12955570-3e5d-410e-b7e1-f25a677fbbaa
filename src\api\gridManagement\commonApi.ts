import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = '/gridInfo'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取不分页网格列表（下拉选择）
export const getGridListWithoutPageApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/getGridInfoComboBox'),
    params
  })

  return res && res.data
}
