export interface sellQuesClassType {
  id?: string
  classifyName: string
}
export interface sellQuesType {
  id?: string
  reQuestionTitle: string
  reQuestionContent: string
  classifyId: string
  classifyName?: string
  reQuestionState: number
}

export interface complaintQueryType {
  id?: string
  complaintReason: string
  qaAnswerId: string
  answerId: string
  complaintType: string
  remarks: string
  handleType: string
  handleInfo: string
  tenantId: string
}

export interface complaintFormType {
  complaintReason: string
  qaAnswerId: string
  answer: string
  qaQuestionTitle: string
  answerId: string
  complaintType: string
  complaintTypeName: string
  isTrue: number
  remarks: string
  handleType: string
  handleInfo: string
  tenantId: string
  startTime: string
  endTime: string
  onlySign: string
  reserveInfo: string
}

export interface complaintResType {
  complaintReason: string
  qaAnswerId: string
  answer: string
  qaQuestionTitle: string
  answerId: string
  complaintType: string
  complaintTypeName: string
  isTrue: number
  remarks: string
  handleType: string
  handleInfo: string
  tenantId: string
  startTime: string
  endTime: string
  onlySign: string
  reserveInfo: string
  createdTime: string
  lastModifiedTime: string
  lastModifiedUserId: string
  lastModifiedUserName: string
  createdUserId: string
  createdUserName: string
  id: string
}

export interface complaintDetailType {
  complaintReason: string
  qaAnswerId: string
  answer: string
  qaQuestionTitle: string
  answerId: string
  complaintType: string
  complaintTypeName: string
  isTrue: number
  remarks: string
  handleType: string
  handleInfo: string
  tenantId: string
  startTime: string
  endTime: string
  onlySign: string
  reserveInfo: string
  createdTime: string
  lastModifiedTime: string
  lastModifiedUserId: string
  lastModifiedUserName: string
  createdUserId: string
  createdUserName: string
  id: string
}

export interface acceptComplaintBodyType {
  qaAnswerId: string
  answerId: string
  isTrue: number
  handleType: string
  handleInfo: string
}

export interface askSessionType {
  sessionId: string
}

export interface askQueryType {
  sessionId?: string
  qaQuestionTitle?: string
}

export interface answerResType {
  qaQuestionTitle: string
  qaAnswerId: string
  answer: string
  knowledgeId: string
  knowledgeType: string
  fileAddress: string
  complaints?: number
  appraise?: number
}

export interface answerLogType {
  id: string
  qaQuestionTitle: string
  userId: string
  createdTime: string
  tenantId: string
}

export interface QandARecordType {
  id: string
  sessionId: string
  qaQuestionTitle: string
  appraise: number
  knowledgeId: string
  knowledgeType: string
  standardQuestion: string
  standardCreatedUser: string
  answer: string
  userId: string
  userName: string
  createdTime: string
  tenantId: string
  complaints: number
}

export interface QaAppraiseQueryType {
  id: string
  appraise: number
}

export interface contactQueryResType {
  id: string
  contactName: string
  contactPhone: string
  userAgreementId: string
  userAgreementName: string
}
