export interface SoleWordType {
  id?: string
  properWords: string
  wordsDefinition: string
}
export interface NearWordType {
  id?: string
  baseWords: string
  synonymsWords: string
}
export interface sensitiveWordType {
  id?: string
  sensitiveWords: string
  remark: string
}

export interface standardWordType {
  id: string
  standardWord: string
}
export interface nearWordType {
  id?: string
  standardWordId: string
  synonymsWords: string
}
