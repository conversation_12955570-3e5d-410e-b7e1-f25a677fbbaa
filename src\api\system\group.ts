import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { GroupType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/groups'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取用户组列表
export const getGroListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<GroupType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取用户组树形
export const getGroTreeApi = async () => {
  const res = await request.get<IResponse<GroupType[]>>({
    url: getUrl('tree')
  })
  return res && res.data
}

// 添加用户组
export const addGroApi = async (data: GroupType) => {
  const res = await request.post<IResponse<GroupType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除用户组
export const delGroApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新用户组
export const updateGroApi = async (data: GroupType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}

// 获取用户组详情
export const getGroDetailApi = async (id: string) => {
  const res = await request.get<IResponse<GroupType>>({
    url: getUrl(id)
  })

  return res && res.data
}
