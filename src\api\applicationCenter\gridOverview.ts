/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-06-04 12:51:04
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'

const request = useAxios()

// 基础路径
const BASE_URL = ''

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 根据租户获取一级网格长列表
export const getGridManagerListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/gridInfo/getFirstGridLeader'),
    params
  })
  return res && res.data
}

// 获取网格总览列表
export const getGridOverviewApi = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/gridInfo/getGridOverview'),
    params
  })
  return res && res.data
}

// 获取村民积分/党员积分排名列表
export const getvillagerInfoJfListApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/score/villagerInfoRank',
    params
  })
  return res && res.data
}

//获取饼图数据
export const getEventStatisticByType = async (params?: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl('/gridEvent/getNumByStatus'),
    params
  })
  return res && res.data
}

// 获取工作积分排名列表
export const getWorkerInfoRankApi = async (params: Recordable) => {
  const res = await request.get<IResponse>({
    url: '/score/workerInfo/workerInfoRank',
    params
  })
  return res && res.data
}
