// import QueryString from 'qs'

export interface ClientType {
  clientId?: string
  clientName: string
  clientIdIssuedAt?: any
  clientSecret: any
  clientSecretExpiresAt: any
  // clientAuthenticationMethod?: Partial<ClientSecreType>
  // authorizationGrantType: Partial<GrantType>
  redirectUris?: any
  authorizationScopes?: any
  clientScopes?: any
  // clientSettings: Partial<SettingType>
  tokenSettings?: any
  id?: string
  belongApplicationId?: string
  clientAuthenticationMethod?: any
  authorizationGrantType?: any
  clientSettings?: any
  accessTokenTimeToLive?: any
  refreshTokenTimeToLive?: any
}
export interface ClientSecreType {
  clientSecretBasic?: boolean
  clientSecretPost?: boolean
}
export interface GrantType {
  authorizationCode?: boolean
  refreshToken?: boolean
  clientCredentials?: boolean
}
export interface SettingType {
  requireAuthoqrizationConsent?: boolean
  belongApplicationId?: string
}
export interface TokenType {
  accessTokenTimeToLive?: string
  refreshTokenTimeToLive?: string
}
