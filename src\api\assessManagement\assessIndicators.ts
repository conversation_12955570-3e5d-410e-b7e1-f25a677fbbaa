/*
 * @Author: 18156018406 <EMAIL>
 * @Date: 2024-07-12 09:25:45
 * @LastEditors: 18156018406 <EMAIL>
 * @LastEditTime: 2024-10-18 08:53:50
 * @FilePath: \szxcy-gov-management\src\api\assessManagement\assessIndicators.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { assessIndicatorType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/assess'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取指标列表
export const getIndicatorListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessIndicatorType>>>({
    url: getUrl('/metrics'),
    params
  })
  return res && res.data
}

// 获取指标详情
export const getIndicatorDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/metrics/${id}`),
    params: { id }
  })
  return res && res.data
}

// 添加指标信息
export const addIndicatorInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/metrics'),
    data
  })
  return res && res.data
}

// 编辑指标信息
export const editIndicatorInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/metrics/${data.id}`),
    data
  })
  return res && res.data
}

// 更新指标信息
export const updateIndicatorInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/metrics/updateStatus/${data.id}`),
    data
  })
  return res && res.data
}

// 删除单个指标
export const delIndicatorInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/metrics/${id}`)
  })
  return res && res.data
}

// 获取指标下拉框列表
export const getIndicatorDropDownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessIndicatorType>>>({
    url: getUrl('/metrics/dropDownList'),
    params
  })
  return res && res.data
}
