/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:10:23
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-02-02 10:11:46
 * @FilePath: \szxcy-gov-management\src\api\operation\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface PackageListType {
  id: string
  areaCode: string
  code: string
  name: string
  price: number
  status: number
  createdUserName: string
  mobile: number
  lastModifiedTime: string
  delete: boolean
}

export interface updatePackageType {
  areaCode: string
  code: string
  name: string
  price: number
  status: number
}

export interface PackageDetailType {
  id: string
  areaCode: string
  code: string
  name: string
  price: number
  status: number
  createdUserName: string
  mobile: number
  lastModifiedTime: string
  delete: boolean
}

export interface DataMigrationType {
  id: string
  areaCode: string
  migrationCode: string
  sumNum: number
  failNum: number
  sucessNum: number
  type: number
  syncStatus: number
  context: string
  setStartTime: string
  startTime: string
  userId: string
  realStartTime: string
  realEndTime: string
  updateUser: string
  updateTime: string
  contextStr: String
}
