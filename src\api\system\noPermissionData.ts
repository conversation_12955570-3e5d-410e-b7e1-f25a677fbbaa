/**
 * 无数据权限接口
 * 包含用户、组织部门等，这里可以获取所有数据
 */

import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import {
  DepartmentsType,
  GroupType,
  OrgDeptTreeDataType,
  OrganizationType,
  UserType
} from './types'
import { getDepartmentsListApi, getOrganizationListApi } from './organization'
import { merge } from 'lodash-es'
import { forEach, listToTree } from '@/utils/tree'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/un-data-permission'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

/***************************用户********************************/
// 获取用户列表
export const getUserListWithoutPerApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<UserType>>>({
    url: getUrl('users'),
    params
  })
  return res && res.data
}

// 获取用户详情
export const getUserDetailWithoutPerApi = async (id: string) => {
  const res = await request.get<IResponse<UserType>>({
    url: getUrl(`users/${id}`)
  })

  return res && res.data
}

/***************************组织********************************/
// 获取组织列表
export const getOrganizationListWithoutPerApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<OrganizationType>>>({
    url: getUrl('organizations'),
    params
  })
  return res && res.data
}

// 获取组织树形列表
export const getOrganizationTreeWithoutPerApi = async () => {
  const res = await request.get<IResponse<OrganizationType[]>>({
    url: getUrl('organizations/tree')
  })
  return res && res.data
}

// 根据id查询组织详情
export const getOrganizationDetailWithoutPerApi = async (id: string) => {
  const res = await request.get<IResponse<OrganizationType>>({
    url: getUrl(`organizations/${id}`)
  })
  return res && res.data
}

export const PREFIX_ORGS = 'ORGS' //  组织ID前缀
export const PREFIX_DEPT = 'DEPT' //  部门ID前缀

const DEFAULT_CONFIG = {
  isIdAddPrefix: true, //是否需要在组织/部门ID前加前缀
  orgIdPrefix: PREFIX_ORGS, // 组织前缀
  deptIdPrefix: PREFIX_DEPT, // 部门前缀
  withoutPer: false
}

// 获取组织/部门树形列表
export const getOrgAndDeptTreeWithOptionsApi = async (config?: {
  isIdAddPrefix?: boolean //是否需要在组织/部门ID前加前缀
  orgIdPrefix?: string // 组织前缀
  deptIdPrefix?: string // 部门前缀
  withoutPer?: boolean //使用无数据权限查询，默认是带权限查询
}): Promise<OrgDeptTreeDataType[]> => {
  const { isIdAddPrefix, orgIdPrefix, deptIdPrefix, withoutPer } = merge(
    DEFAULT_CONFIG,
    config ? config : {}
  )
  let orgTask: Promise<IResponse<PageType<OrganizationType>>>
  let deptTask: Promise<IResponse<PageType<DepartmentsType>>>
  if (withoutPer) {
    orgTask = getOrganizationListWithoutPerApi({ size: -1, sort: 'orderNo' })
    deptTask = getDepartmentsListWithoutPerApi({ size: -1, sort: 'orderNo' })
  } else {
    orgTask = getOrganizationListApi({ size: -1, sort: 'orderNo' })
    deptTask = getDepartmentsListApi({ size: -1, sort: 'orderNo' })
  }

  return Promise.all([orgTask, deptTask]).then((resArr) => {
    let treeData: OrgDeptTreeDataType[] = []
    if (resArr[0] && resArr[0].data && resArr[0].data.content) {
      const orgList = resArr[0].data.content.map((data) => ({
        id: (isIdAddPrefix ? orgIdPrefix : '') + data.id,
        parentId: (isIdAddPrefix ? orgIdPrefix : '') + data.parentId,
        label: data.name,
        srcId: data.id,
        type: orgIdPrefix
      }))
      treeData = listToTree(orgList)
    }
    if (resArr[1] && resArr[1].data && resArr[1].data.content) {
      const deptList = resArr[1].data.content.map((data) => ({
        id: (isIdAddPrefix ? deptIdPrefix : '') + data.id,
        parentId: (isIdAddPrefix ? deptIdPrefix : '') + data.parentId,
        orgId: (isIdAddPrefix ? orgIdPrefix : '') + data.organizationId,
        label: data.name,
        srcId: data.id,
        type: deptIdPrefix
      }))
      const deptTree = listToTree(deptList)
      const tmpDeptTree = {}
      deptTree.forEach((dept) => {
        if (dept.orgId) {
          if (tmpDeptTree[dept.orgId]) {
            tmpDeptTree[dept.orgId].push(dept)
          } else {
            tmpDeptTree[dept.orgId] = [dept]
          }
        }
      })
      forEach(treeData, (org) => {
        if (tmpDeptTree[org.id]) {
          if (!org.children) {
            org.children = []
          }
          org.children.push(...tmpDeptTree[org.id])
        }
      })
    }
    return treeData
  })
}

/***************************用户组********************************/
// 获取用户组列表
export const getUserGroupListWithoutPerApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<GroupType>>>({
    url: getUrl('groups'),
    params
  })
  return res && res.data
}

// 获取用户组树形
export const getUserGroupTreeWithoutPerApi = async () => {
  const res = await request.get<IResponse<GroupType[]>>({
    url: getUrl('groups/tree')
  })
  return res && res.data
}

// 获取用户组详情
export const getUserGroupDetailWithoutPerApi = async (id: string) => {
  const res = await request.get<IResponse<GroupType>>({
    url: getUrl(`groups/${id}`)
  })

  return res && res.data
}

/***************************用户组********************************/

// 获取部门列表
export const getDepartmentsListWithoutPerApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<DepartmentsType>>>({
    url: getUrl('departments'),
    params
  })
  return res && res.data
}

// 获取部门树
export const getDepartmentsTreeWithoutPerApi = async (id: string) => {
  const res = await request.get<IResponse<DepartmentsType[]>>({
    url: getUrl(`departments/${id}/tree`)
  })

  return res && res.data
}

// 获取部门详情
export const getDepartmentsDetailWithoutPerApi = async (id: string) => {
  const res = await request.get<IResponse<DepartmentsType>>({
    url: getUrl(`departments/${id}`)
  })
  return res && res.data
}
