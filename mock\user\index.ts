import { UserType } from '@/api/system/types'
import { config } from '@/config/axios/config'
import { MockMethod } from 'vite-plugin-mock'

const { result_code } = config

const timeout = 1000

const userInfo: Partial<UserType> = {
  id: '2ebcd6fc-7add-4895-8ed8-885440935e7e',
  organization: {
    id: 'd79a0f2c-4d61-4386-991f-d773ec43f024',
    code: 'test01',
    name: '测试创建组织01'
  },
  username: 'test01',
  roles: [
    {
      id: '592ab077-bd8a-42be-be6c-89465c336d42',
      code: 'super-admin',
      name: '超级管理员'
    }
  ],
  groups: [
    {
      id: '********-60aa-4198-adcf-093f56886c0d',
      code: 'test01',
      name: '用户组测试更新1'
    }
  ],
  positions: [
    {
      id: 'e19c4c0d-c961-481a-b5b3-d59cd3b0575f',
      code: 'test01',
      name: '测试更新1'
    }
  ],
  name: '测试新建用户',
  status: 0,
  sex: 0,
  type: 0,
  enabled: true,
  accountNonExpired: true,
  credentialsNonExpired: true,
  accountNonLocked: true,
  permission: {
    children: [
      {
        code: '1000',
        title: '系统管理',
        name: 'System',
        component: '#',
        path: '/system',
        redirect: 'noredirect',
        icon: 'svg-icon:system',
        meta: { alwaysShow: true },
        children: [
          {
            code: '1000',
            title: '租户管理',
            name: 'Tenant',
            component: 'System/Tenant',
            path: 'tenant',
            redirect: 'noredirect'
          },
          {
            code: '1001',
            title: '应用管理',
            name: 'Application',
            component: 'System/Application',
            path: 'application',
            redirect: 'noredirect'
          },
          {
            code: '1002',
            title: '权限管理',
            name: 'Permission',
            component: 'System/Permission',
            path: 'permission',
            redirect: 'noredirect'
          },
          {
            code: '1003',
            title: '字典管理',
            name: 'Dictionary',
            component: 'System/Dictionary/Dictionary',
            path: 'dictionary',
            redirect: 'noredirect'
          },
          {
            code: '1004',
            title: '资源管理',
            name: 'Resource',
            component: 'System/Resource',
            path: 'resource',
            redirect: 'noredirect'
          },
          {
            id: '1005',
            code: '1005',
            title: 'API管理',
            name: 'Api',
            component: 'System/Api',
            path: 'apis',
            redirect: 'noredirect'
          },
          {
            id: '1006',
            code: '1006',
            title: '岗位管理',
            name: 'Position',
            component: 'System/Position',
            path: 'position',
            redirect: 'noredirect'
          },
          {
            id: '1007',
            code: '1007',
            title: '用户组管理',
            name: 'Group',
            component: 'System/Group',
            path: 'group',
            redirect: 'noredirect'
          },
          {
            id: '1008',
            code: '1008',
            title: '组织管理',
            name: 'Organization',
            component: 'System/Organization',
            path: 'organization',
            redirect: 'noredirect'
          },
          {
            id: '1009',
            code: '1009',
            title: '角色管理',
            name: 'Role',
            component: 'System/Role',
            path: 'role',
            redirect: 'noredirect'
          },
          {
            id: '1010',
            code: '1010',
            title: '用户管理',
            name: 'User',
            component: 'System/User',
            path: 'user',
            redirect: 'noredirect'
          }
        ]
      }
    ]
  }
}

export default [
  // 用户信息
  {
    url: '/user/oauth2/currentUserInfo',
    method: 'get',
    response: () => {
      return userInfo
    }
  },
  // 登录接口
  {
    url: '/user/login',
    method: 'post',
    timeout,
    response: () => {
      return {
        code: 200,
        data: {
          token:
            'Bearer cvXP1eufUY1oInVoRomTcxVmKXfETp2PPeCvPLY4LolujknO2lhw-E18I3Pq-Izfld_igNrXtpnakHvbfALWhE0MIFZY86E7aFP_Uh2_bd1wmWycBMo3dOn9RVKaDTGO'
        },
        message: '成功'
      }
    }
  },
  // 退出接口
  {
    url: '/user/loginOut',
    method: 'get',
    timeout,
    response: () => {
      return {
        status: result_code,
        result: null
      }
    }
  }
] as MockMethod[]
