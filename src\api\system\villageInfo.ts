/*
 * @Author: srd17333991128 <EMAIL>
 * @Date: 2024-01-29 16:02:42
 * @LastEditors: srd17333991128 <EMAIL>
 * @LastEditTime: 2024-04-01 15:04:37
 * @FilePath: \szxcy-gov-management\src\api\operation\packages.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
// import type { PackageListType, updatePackageType, PackageDetailType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/yhydVillageInformation'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 查询县镇乡村基本信息
export const getVillageInfoApi = async (orgCode: string, params: Recordable) => {
  const res = await request.get<IResponse>({
    url: getUrl(orgCode),
    params
  })
  return res && res.data
}

// 村镇初始化区域经纬度
export const getAddressByCoordinateApi = async (tenantId) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/getAddressByCoordinate/${tenantId}`)
  })
  return res && res.data
}

// 编辑县镇乡村基本信息
export const editVillageInfoApi = async (data: Recordable) => {
  const res = await request.post<IResponse>({
    url: getUrl('/yhydVillageSave'),
    data
  })
  return res && res.data
}

// 编辑县镇乡村基本信息
export const updateVillageInfoApi = async (id: string, data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(id),
    data
  })
  return res && res.data
}
