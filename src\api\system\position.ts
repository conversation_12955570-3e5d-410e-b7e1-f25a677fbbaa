import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import { PositionType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/system/positions'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取岗位列表
export const getPositionListApi = async (params?: Recordable) => {
  const res = await request.get<IResponse<PageType<PositionType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取岗位详情
export const getPositionDetailApi = async (id: string) => {
  const res = await request.get<IResponse<PositionType>>({
    url: getUrl(id)
  })

  return res && res.data
}

// 添加岗位
export const addPositionApi = async (data: PositionType) => {
  const res = await request.post<IResponse<PositionType>>({
    url: getUrl(),
    data
  })
  return res && res.data
}

// 删除岗位
export const delPositionApi = async (ids: string[]) => {
  let id = ''
  if (ids && ids.length > 0) {
    id = ids[0]
  }
  const res = await request.delete<IResponse>({
    url: getUrl(id)
  })
  return res && res.data
}

// 更新岗位
export const updatePositionApi = async (data: PositionType) => {
  const res = await request.put<IResponse>({
    url: getUrl(data.id),
    data
  })
  return res && res.data
}
