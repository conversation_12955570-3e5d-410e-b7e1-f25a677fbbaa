import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { assessTemplateType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/assess'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取模版列表
export const getTemplateListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessTemplateType>>>({
    url: getUrl('/template'),
    params
  })
  return res && res.data
}

// 获取模版详情
export const getTemplateDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: getUrl(`/template/${id}`),
    params: { id }
  })
  return res && res.data
}

// 添加模版信息
export const addTemplateInfoItemApi = async (data: any) => {
  const res = await request.post<IResponse>({
    url: getUrl('/template'),
    data
  })
  return res && res.data
}

// 编辑模版信息
export const editTemplateInfoItemApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/template/${data.id}`),
    data
  })
  return res && res.data
}

// 考核模版开启关闭
export const changeTemplateStatusApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/template/updateStatus/${data.id}`),
    data: { status: data.status }
  })
  return res && res.data
}

// 删除单个模版
export const delTemplateInfoApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/template/${id}`),
    params: { id }
  })
  return res && res.data
}

// 获取模版下拉框列表
export const getTemplateDropDownListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<assessTemplateType>>>({
    url: getUrl('/template/dropDownList'),
    params
  })
  return res && res.data
}
