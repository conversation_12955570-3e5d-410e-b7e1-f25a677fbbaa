import { useAxios } from '@/hooks/web/useAxios'
// import { FormDataType } from '@/api/formDesign/types'

const request = useAxios()
// 获取当前配置的天气预警信息
export const getWeatherConfig = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/warmReminder/weatherWarningConfig',
    params
  })

  return res && res.data
}
// 获取当前配置的天气预警信息
export const setWeatherConfig = async (data: any) => {
  const res = await request.put<IResponse>({
    url: '/warmReminder/weatherWarningConfig',
    data
  })

  return res && res.data
}
// 获取节日关怀设置列表
export const getHolidayCareConfigApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<any>>>({
    url: `/warmReminder/holidayCareConfig`,
    params
  })
  return res && res.data
}
// 删除节日关怀设置列表
export const deleteHolidayCareConfigApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: `/warmReminder/holidayCareConfig/${id}`
  })
  return res && res.data
}
// 新增节日关怀设置
export const addHolidayCareConfig = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/warmReminder/holidayCareConfig',
    data
  })

  return res && res.data
}
// 获取节日关怀设置详情
export const getHolidayCareDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: `/warmReminder/holidayCareConfig/${id}`
  })
  return res && res.data
}
// 获取当前配置的天气预警信息
export const setHolidayCareDetailApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: `/warmReminder/holidayCareConfig/${data.id}`,
    data
  })

  return res && res.data
}
// 新增日常关怀设置
export const addDailyReminderConfig = async (data: any) => {
  const res = await request.post<IResponse>({
    url: '/warmReminder/dailyReminderConfig',
    data
  })

  return res && res.data
}
// 获取日常关怀设置列表
export const getDailyReminderConfigApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<any>>>({
    url: `/warmReminder/dailyReminderConfig`,
    params
  })
  return res && res.data
}
// 删除日常关怀设置列表
export const deleteDailyReminderConfigApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: `/warmReminder/dailyReminderConfig/${id}`
  })
  return res && res.data
}
// 获取日常关怀设置详情
export const getDailyReminderConfigDetailApi = async (id: string) => {
  const res = await request.get<IResponse>({
    url: `/warmReminder/dailyReminderConfig/${id}`
  })
  return res && res.data
}
// 编辑日常关怀信息
export const setDailyReminderConfigDetailApi = async (data: any) => {
  const res = await request.put<IResponse>({
    url: `/warmReminder/dailyReminderConfig/${data.id}`,
    data
  })

  return res && res.data
}
// 编辑节日关怀信息
export const setUpdateHolidayCareStatusApi = async (data: any) => {
  const obj = {
    holidayPushFlag: data.holidayPushFlag
  }
  const res = await request.put<IResponse>({
    url: `/warmReminder/holidayCareConfig/updateHolidayCareStatus/${data.id}`,
    params: obj
  })

  return res && res.data
}
// 编辑日常关怀信息
export const setUpdateDailyReminderStatusApi = async (data: any) => {
  const obj = {
    reminderPushFlag: data.reminderPushFlag
  }
  const res = await request.put<IResponse>({
    url: `/warmReminder/dailyReminderConfig/updateDailyReminderStatus/${data.id}`,
    params: obj
  })

  return res && res.data
}
// 获取短信设置列表
export const getMessageConfigApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<any>>>({
    url: `/messageConfig`,
    params
  })
  return res && res.data
}
// 编辑短信设置列表
export const updateMessageConfigApi = async (data: any) => {
  const res = await request.put<any>({
    url: `/messageConfig/${data.id}`,
    data
  })
  return res && res.data
}
// 查看是否有人员存在
export const checkHavePersonOrNot = async (params: any) => {
  const res = await request.get<IResponse>({
    url: '/users/user/checkHavePersonOrNot',
    params
  })

  return res && res.data
}
