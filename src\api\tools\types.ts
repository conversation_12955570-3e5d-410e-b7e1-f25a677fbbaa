export interface GenColumnType {
  id: string
  tableId: string
  columnName: string
  columnComment: string
  columnType: string
  javaType: string
  javaField: string
  isPk: string
  isIncrement: string
  isRequired: string
  isInsert: string
  isEdit: string
  isList: string
  isQuery: string
  queryType: string
  htmlType: string
  dictType: string
  sort: number
  list: boolean
  required: boolean
  pk: boolean
  insert: boolean
  edit: boolean
  query: boolean
}

export interface GenTableType {
  id: string
  tableName: string
  tableComment: string
  subTableName: string
  subTableFkName: string
  className: string
  tplCategory: string
  packageName: string
  moduleName: string
  businessName: string
  functionName: string
  functionAuthor: string
  genType: string
  genPath: string
  pkColumn: GenColumnType
  subTable: string
  columns: GenColumnType[]
  options: string
  treeCode: string
  treeParentCode: string
  treeName: string
  parentMenuId: string
  parentMenuName: string
  createdTime: string
  lastModifiedTime: string
  lastModifiedUserId: string
  lastModifiedUserName: string
  createdUserId: string
  createdUserName: string
  sub: boolean
  tree: boolean
  crud: boolean
  params: Nullable<Recordable>
  description: string
}

export interface PreviewDataType {
  templateName: string
  templateContent: string
}
