import { useAxios, resolveUrl } from '@/hooks/web/useAxios'
import type { EventType } from './types'

const request = useAxios()

// 基础路径
const BASE_URL = '/partyActivitys'

// 获取路径
const getUrl = (path?: string): string => {
  return resolveUrl(BASE_URL, path)
}

// 获取活动列表
export const getEventListApi = async (params: Recordable) => {
  const res = await request.get<IResponse<PageType<EventType>>>({
    url: getUrl(),
    params
  })
  return res && res.data
}

// 获取党活动详情
export const getEventDetailApi = async (id: string) => {
  const res = await request.get<IResponse<EventType>>({
    url: getUrl(id)
  })
  return res && res.data
}

// 党活动删除
export const delEventApi = async (id: string) => {
  const res = await request.delete<IResponse>({
    url: getUrl(`/${id}`)
  })

  return res && res.data
}

// 新建党活动
export const addEventApi = async (data: Partial<EventType>) => {
  const res = await request.post<IResponse>({
    url: getUrl(),
    data
  })

  return res && res.data
}

// 编辑党活动
export const updateEventApi = async (data: Partial<EventType>, id: string) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/${id}`),
    data
  })

  return res && res.data
}

// 更新指派对象
export const updateEventObjectApi = async (data: Recordable) => {
  const res = await request.put<IResponse>({
    url: getUrl(`/partyActivityObject`),
    data
  })

  return res && res.data
}
