import { config } from '@/config/axios/config'
import { MockMethod } from 'vite-plugin-mock'

const { result_code } = config

const timeout = 1000

const adminList = [
  {
    path: '/template',
    component: '#',
    redirect: '/template/template',
    name: 'Template',
    meta: {
      title: 'templateDemo.title',
      icon: 'svg-icon:message'
      // alwaysShow: true
    },
    children: [
      {
        path: 'template',
        name: 'Template1',
        component: 'Template/Template',
        meta: {
          title: 'templateDemo.title',
          icon: 'svg-icon:peoples'
        }
      }
    ]
  }
]

const testList: string[] = ['/template', '/template/template']

export default [
  // 列表接口
  {
    url: '/role/list',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { roleName } = query
      return {
        code: result_code,
        data: {
          list: roleName === 'admin' ? adminList : testList
        }
      }
    }
  }
] as MockMethod[]
